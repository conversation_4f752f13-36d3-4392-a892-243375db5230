name: CI

on: workflow_dispatch

# on:
#   push:
#     branches:
#       - main
#       - release/*
#   pull_request:
#     branches:
#       - main
#       - release/*

jobs:
  windows:
    name: Windows
    runs-on: windows-2022
    timeout-minutes: 60
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node_modules archive
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: ".build/node_modules_cache"
          key: "${{ runner.os }}-cacheNodeModulesArchive-${{ steps.nodeModulesCacheKey.outputs.value }}"
      - name: Extract node_modules archive
        if: ${{ steps.cacheNodeModules.outputs.cache-hit == 'true' }}
        run: 7z.exe x .build/node_modules_cache/cache.7z -aos
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          npm_config_foreground_scripts: "true"
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci
      - name: Create node_modules archive
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: |
          mkdir -Force .build
          node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
          mkdir -Force .build/node_modules_cache
          7z.exe a .build/node_modules_cache/cache.7z -mx3 `@.build/node_modules_list.txt

      - name: Compile and Download
        run: npm exec -- npm-run-all -lp compile "electron x64" playwright-install download-builtin-extensions

      - name: Compile Integration Tests
        run: npm run compile
        working-directory: test/integration/browser

      - name: Run Unit Tests (Electron)
        run: .\scripts\test.bat

      - name: Run Unit Tests (node.js)
        run: npm run test-node

      - name: Run Unit Tests (Browser, Chromium)
        run: npm run test-browser-no-install -- --browser chromium

      - name: Run Integration Tests (Electron)
        run: .\scripts\test-integration.bat

      - name: Run Integration Tests (Browser, Firefox)
        timeout-minutes: 20
        run: .\scripts\test-web-integration.bat --browser firefox

      - name: Run Integration Tests (Remote)
        timeout-minutes: 20
        run: .\scripts\test-remote-integration.bat

  linux:
    name: Linux
    runs-on: ubuntu-latest
    timeout-minutes: 40
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      # TODO: rename azure-pipelines/linux/xvfb.init to github-actions
      - name: Setup Build Environment
        run: |
          sudo apt-get update
          sudo apt-get install -y libxkbfile-dev pkg-config libkrb5-dev libxss1 xvfb libgtk-3-0 libgbm1
          sudo cp build/azure-pipelines/linux/xvfb.init /etc/init.d/xvfb
          sudo chmod +x /etc/init.d/xvfb
          sudo update-rc.d xvfb defaults
          sudo service xvfb start

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-cacheNodeModulesLinux-${{ steps.nodeModulesCacheKey.outputs.value }}
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci

      - name: Compile and Download
        run: npm exec -- npm-run-all -lp compile "electron x64" playwright-install download-builtin-extensions

      - name: Compile Integration Tests
        run: npm run compile
        working-directory: test/integration/browser

      - name: Run Unit Tests (Electron)
        id: electron-unit-tests
        run: DISPLAY=:10 ./scripts/test.sh

      - name: Run Unit Tests (node.js)
        id: nodejs-unit-tests
        run: npm run test-node

      - name: Run Unit Tests (Browser, Chromium)
        id: browser-unit-tests
        run: DISPLAY=:10 npm run test-browser-no-install -- --browser chromium

      - name: Run Integration Tests (Electron)
        id: electron-integration-tests
        run: DISPLAY=:10 ./scripts/test-integration.sh

      - name: Run Integration Tests (Browser, Chromium)
        id: browser-integration-tests
        run: DISPLAY=:10 ./scripts/test-web-integration.sh --browser chromium

      - name: Run Integration Tests (Remote)
        id: electron-remote-integration-tests
        timeout-minutes: 15
        run: DISPLAY=:10 ./scripts/test-remote-integration.sh

  darwin:
    name: macOS
    runs-on: macos-latest
    timeout-minutes: 40
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-cacheNodeModulesMacOS-${{ steps.nodeModulesCacheKey.outputs.value }}
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci

      - name: Compile and Download
        run: npm exec -- npm-run-all -lp compile "electron x64" playwright-install download-builtin-extensions

      - name: Compile Integration Tests
        run: npm run compile
        working-directory: test/integration/browser

      # This is required for SecretStorage unittests
      - name: Create temporary keychain
        run: |
          security create-keychain -p pwd $RUNNER_TEMP/buildagent.keychain
          security default-keychain -s $RUNNER_TEMP/buildagent.keychain
          security unlock-keychain -p pwd $RUNNER_TEMP/buildagent.keychain

      - name: Run Unit Tests (Electron)
        run: DISPLAY=:10 ./scripts/test.sh

      - name: Run Unit Tests (node.js)
        run: npm run test-node

      - name: Run Unit Tests (Browser, Chromium)
        run: DISPLAY=:10 npm run test-browser-no-install -- --browser chromium

      - name: Run Integration Tests (Electron)
        run: DISPLAY=:10 ./scripts/test-integration.sh

      - name: Run Integration Tests (Browser, Webkit)
        run: DISPLAY=:10 ./scripts/test-web-integration.sh --browser webkit

      - name: Run Integration Tests (Remote)
        timeout-minutes: 15
        run: DISPLAY=:10 ./scripts/test-remote-integration.sh

  hygiene:
    name: Hygiene and Layering
    runs-on: ubuntu-latest
    timeout-minutes: 40
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-cacheNodeModulesLinux-${{ steps.nodeModulesCacheKey.outputs.value }}
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci

      - name: Download Playwright
        run: npm run playwright-install

      - name: Run Hygiene Checks
        run: npm run gulp hygiene

      - name: Run Valid Layers Checks
        run: npm run valid-layers-check

      - name: Run Property Init Order Checks
        run: npm run property-init-order-check

      - name: Compile /build/
        run: npm run compile
        working-directory: build

      - name: Check clean git state
        run: ./.github/workflows/check-clean-git-state.sh

      - name: Run eslint
        run: npm run eslint

      - name: Run vscode-dts Compile Checks
        run: npm run vscode-dts-compile-check

      - name: Run Trusted Types Checks
        run: npm run tsec-compile-check
