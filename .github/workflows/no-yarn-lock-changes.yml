name: Prevent yarn.lock changes in PRs
on: [pull_request]

jobs:
  main:
    name: Prevent yarn.lock changes in PRs
    runs-on: ubuntu-latest
    steps:
      - uses: octokit/request-action@v2.x
        id: get_permissions
        with:
          route: GET /repos/microsoft/vscode/collaborators/{username}/permission
          username: ${{ github.event.pull_request.user.login }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Set control output variable
        id: control
        run: |
          echo "user: ${{ github.event.pull_request.user.login }}"
          echo "role: ${{ from<PERSON>son(steps.get_permissions.outputs.data).permission }}"
          echo "is dependabot: ${{ github.event.pull_request.user.login == 'dependabot[bot]' }}"
          echo "should_run: ${{ !contains(from<PERSON><PERSON>('["admin", "maintain", "write"]'), from<PERSON><PERSON>(steps.get_permissions.outputs.data).permission) }}"
          echo "should_run=${{ !contains(from<PERSON><PERSON>('["admin", "maintain", "write"]'), from<PERSON><PERSON>(steps.get_permissions.outputs.data).permission) && github.event.pull_request.user.login != 'dependabot[bot]' }}" >> $GITHUB_OUTPUT
      - name: Get file changes
        uses: trilom/file-changes-action@ce38c8ce2459ca3c303415eec8cb0409857b4272
        if: ${{ steps.control.outputs.should_run == 'true' }}
      - name: Check for lockfile changes
        if: ${{ steps.control.outputs.should_run == 'true' }}
        run: |
          cat $HOME/files.json | jq -e 'any(test("yarn\\.lock$|Cargo\\.lock$")) | not' \
            || (echo "Changes to yarn.lock/Cargo.lock files aren't allowed in PRs." && exit 1)
