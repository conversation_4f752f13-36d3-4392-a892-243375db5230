name: Basic checks

on: workflow_dispatch

# on:
#   push:
#     branches:
#       - main
#   pull_request:
#     branches:
#       - main

jobs:
  main:
    if: github.ref != 'refs/heads/main'
    name: Compilation, Unit and Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 40
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      # TODO: rename azure-pipelines/linux/xvfb.init to github-actions
      - name: Setup Build Environment
        run: |
          sudo cp build/azure-pipelines/linux/xvfb.init /etc/init.d/xvfb
          sudo chmod +x /etc/init.d/xvfb
          sudo update-rc.d xvfb defaults
          sudo service xvfb start

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-cacheNodeModulesLinux-${{ steps.nodeModulesCacheKey.outputs.value }}
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci

      - name: Compile and Download
        run: npm exec -- npm-run-all -lp compile "electron x64"

      - name: Run Unit Tests
        id: electron-unit-tests
        run: DISPLAY=:10 ./scripts/test.sh

      - name: Run Integration Tests (Electron)
        id: electron-integration-tests
        run: DISPLAY=:10 ./scripts/test-integration.sh

  hygiene:
    if: github.ref != 'refs/heads/main'
    name: Hygiene and Layering
    runs-on: ubuntu-latest
    timeout-minutes: 40
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-cacheNodeModulesLinux-${{ steps.nodeModulesCacheKey.outputs.value }}
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci

      - name: Run Hygiene Checks
        run: npm run gulp hygiene

      - name: Run Valid Layers Checks
        run: npm run valid-layers-check

      - name: Run Property Init Order Checks
        run: npm run property-init-order-check

      - name: Compile /build/
        run: npm run compile
        working-directory: build

      - name: Check clean git state
        run: ./.github/workflows/check-clean-git-state.sh

      - name: Run eslint
        run: npm run eslint

      - name: Run vscode-dts Compile Checks
        run: npm run vscode-dts-compile-check

      - name: Run Trusted Types Checks
        run: npm run tsec-compile-check

  warm-cache:
    name: Warm up node modules cache
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    timeout-minutes: 40
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Compute node modules cache key
        id: nodeModulesCacheKey
        run: echo "value=$(node build/azure-pipelines/common/computeNodeModulesCacheKey.js)" >> $GITHUB_OUTPUT
      - name: Cache node modules
        id: cacheNodeModules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-cacheNodeModulesLinux-${{ steps.nodeModulesCacheKey.outputs.value }}
      - name: Get npm cache directory path
        id: npmCacheDirPath
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
      - name: Cache npm directory
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        uses: actions/cache@v4
        with:
          path: ${{ steps.npmCacheDirPath.outputs.dir }}
          key: ${{ runner.os }}-npmCacheDir-${{ steps.nodeModulesCacheKey.outputs.value }}
          restore-keys: ${{ runner.os }}-npmCacheDir-
      - name: Execute npm
        if: ${{ steps.cacheNodeModules.outputs.cache-hit != 'true' }}
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
        run: npm ci
