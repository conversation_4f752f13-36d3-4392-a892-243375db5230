name: "Rich Navigation Indexing"
on:
  workflow_dispatch:
  push:
    branches:
      - main
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'

jobs:
  richnav:
    runs-on: windows-2022
    steps:
      - uses: actions/checkout@v3

      - uses: actions/cache@v3
        id: caching-stage
        name: Cache VS Code dependencies
        with:
          path: node_modules
          key: ${{ runner.os }}-dependencies-${{ hashfiles('package-lock.json') }}
          restore-keys: ${{ runner.os }}-dependencies-

      - uses: actions/setup-node@v3
        with:
          node-version-file: .nvmrc

      - name: Install dependencies
        if: steps.caching-stage.outputs.cache-hit != 'true'
        run: npm ci
        env:
          npm_config_foreground_scripts: "true"

      - uses: microsoft/RichCodeNavIndexer@v0.1
        with:
          languages: typescript
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          configFiles: .lsifrc.json
        continue-on-error: true
