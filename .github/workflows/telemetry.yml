name: 'Telemetry'
on:
  pull_request:
jobs:
  check-metdata:
    name: 'Check metadata'
    runs-on: 'ubuntu-latest'

    steps:
      - uses: 'actions/checkout@v4'

      - uses: 'actions/setup-node@v4'
        with:
          node-version: 'lts/*'

      - name: 'Run vscode-telemetry-extractor'
        run: 'npx --package=@vscode/telemetry-extractor@1.14.0 --yes vscode-telemetry-extractor -s .'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
