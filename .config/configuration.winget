# yaml-language-server: $schema=https://aka.ms/configuration-dsc-schema/0.2
# Reference: https://github.com/microsoft/vscode/wiki/How-to-Contribute
properties:
  resources:
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      directives:
        description: Install Git
        # Requires elevation for the set operation (i.e., for installing the package)
        securityContext: elevated
      settings:
        id: Git.Git
        source: winget
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      id: npm
      directives:
        description: Install NodeJS version 20
        # Requires elevation for the set operation (i.e., for installing the package)
        securityContext: elevated
      settings:
        id: OpenJS.NodeJS.LTS
        version: "20.14.0"
        source: winget
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      directives:
        description: Install Python 3.10
      settings:
        id: Python.Python.3.10
        source: winget
    - resource: Microsoft.WinGet.DSC/WinGetPackage
      id: vsPackage
      directives:
        description: Install Visual Studio 2022 (any edition is OK)
        # Requires elevation for the set operation (i.e., for installing the package)
        securityContext: elevated
      settings:
        id: Microsoft.VisualStudio.2022.BuildTools
        source: winget
    - resource: Microsoft.VisualStudio.DSC/VSComponents
      dependsOn:
        - vsPackage
      directives:
        description: Install required VS workloads
        # Requires elevation for the get and set operations
        securityContext: elevated
        allowPrerelease: true
      settings:
        productId: Microsoft.VisualStudio.Product.BuildTools
        channelId: VisualStudio.17.Release
        includeRecommended: true
        components:
          - Microsoft.VisualStudio.Workload.VCTools
    - resource: NpmDsc/NpmInstall
      dependsOn:
        - npm
      directives:
        description: Install dependencies
        allowPrerelease: true
      settings:
        PackageDirectory: '${WinGetConfigRoot}\..\'
  configurationVersion: 0.2.0
