# ISSUE 模板示例

## 使用要求

1. 必须是 yaml 格式文件，且文件名以 `.yaml` 或 `.yml` 结尾
2. 必须放在 `.cnb/ISSUE_TEMPLATE` 目录下

## 模板格式

```yml
name:                                # 模板标题，必填
description:                         # 模板描述
body:                                # 模板内容，必填
  - type:                            # 内容区类型，必填。支持 input, markdown, textarea
    attributes:
      render:                        # type: textarea 生效。值只有 text
      label:                         # 内容区标题。当 type: markdown 非必填
    validations:
      required:                      # 标记内容区是否必填。值 true 或 false
  - type:                            # 另一个内容区，可放置多个
    attributes:
      render:
      label:
    validations:
      required:
```
