# mac构建相关
MAC_RUNNER: &MAC_RUNNER
  tags:
    - cnb:arch:custom
    - cnb:aicopilot:mac

STAGE_TIMESTAMP: &STAGE_TIMESTAMP
  - name: timestamp
    script: |
      NOW=$(date +%s)
      BASELINE=$(date -j -f "%Y-%m-%d %H:%M:%S" "2025-06-24 00:00:00" +%s)
      echo $((NOW - BASELINE))
    exports:
      info: TIMESTAMP

STAGE_MAC_ARM_BUILD: &STAGE_MAC_ARM_BUILD
  - name: mac arm构建
    jobs:
      - name: mac arm构建
        timeout: 1h
        script: |
          sh build_macos_min.sh arm64 output-name=$ARM_OUTPUT_NAME
      - name: 查看产物
        script: ls ./result/
      - name: 上传arm zip产物
        script: |
          coscmd upload ./result/$ARM_OUTPUT_NAME.zip $COS_PATH_MAC_ARM
      - name: 输出arm zip产物地址
        script:
          - echo $COS_PATH_PREFIX$COS_PATH_MAC_ARM$ARM_OUTPUT_NAME.zip
      - name: 上传arm dmg产物
        script: |
          coscmd upload ./result/$ARM_OUTPUT_NAME.dmg $COS_PATH_MAC_ARM
      - name: 输出arm dmg产物地址
        script:
          - echo $COS_PATH_PREFIX$COS_PATH_MAC_ARM$ARM_OUTPUT_NAME.dmg
      - name: 上传 arm zip产物到附件
        retry: 5
        script: |
          git-cnb commit asset-upload  -f ./result/$ARM_OUTPUT_NAME.zip -s ${CNB_COMMIT}
      - name: 上传 arm dmg产物到附件
        retry: 5
        script: |
          git-cnb commit asset-upload  -f ./result/$ARM_OUTPUT_NAME.dmg -s ${CNB_COMMIT}

STAGE_MAC_x64_BUILD: &STAGE_MAC_x64_BUILD
  - name: mac x64构建
    if: |
      [ "$BUILD_X64" = "true" ]
    jobs:
      - name: mac x64构建
        timeout: 1h
        script: |
          sh build_macos_min.sh x64 output-name=$X64_OUTPUT_NAME
      - name: 查看产物
        script: ls ./result/
      - name: 上传X64 zip产物
        script: |
          coscmd upload ./result/$X64_OUTPUT_NAME.zip $COS_PATH_MAC_x64
      - name: 输出X64 zip产物地址
        script:
          - echo $COS_PATH_PREFIX$COS_PATH_MAC_x64$X64_OUTPUT_NAME.zip
      - name: 上传X64 dmg产物
        script: |
          coscmd upload ./result/$X64_OUTPUT_NAME.dmg $COS_PATH_MAC_x64
      - name: 输出X64 dmg产物地址
        script:
          - echo $COS_PATH_PREFIX$COS_PATH_MAC_x64$X64_OUTPUT_NAME.dmg
      - name: 上传 X64 zip产物到附件
        retry: 5
        script: |
          git-cnb commit asset-upload  -f ./result/$X64_OUTPUT_NAME.zip -s ${CNB_COMMIT}
      - name: 上传 X64 dmg产物到附件
        retry: 5
        script: |
          git-cnb commit asset-upload  -f ./result/$X64_OUTPUT_NAME.dmg -s ${CNB_COMMIT}

STAGE_MAC_ARM_INIT: &STAGE_MAC_ARM_INIT
  - name: 获取COS_PATH_MAC_ARM
    script: echo /web/aiide/darwin-arm64/
    exports:
      info: COS_PATH_MAC_ARM

STAGE_MAC_x64_INIT: &STAGE_MAC_x64_INIT
  - name: 获取COS_PATH_MAC_x64
    script: echo /web/aiide/darwin/
    exports:
      info: COS_PATH_MAC_x64

STAGE_VERSION_MAC: &STAGE_VERSION_MAC
  - name: version
    script:
      - echo $(cat product.json | grep -o '"genieVersion"\s*:\s*"[^"]*"' | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+')
    exports:
      info: PACKAGE_VERSION

STAGE_MAC_CI: &STAGE_MAC_CI
  - name: mac 编译通过性检测
    jobs:
      - name: max x64检查
        timeout: 1h
        if: |
          [ "$BUILD_X64" = "true" ]
        retry: 2
        script: |
          sh build_macos_ci.sh x64
      - name: max arm检查
        timeout: 1h
        retry: 2
        script: |
          sh build_macos_ci.sh arm64

master:
  pull_request:
    - name: mac 平台检查
      git:
        enable: true
        submodules:
          enable: true
      wework:
        silentStage: true
      env:
        BUILD_X64: false
      runner: *MAC_RUNNER
      stages:
        - *STAGE_MAC_CI

  pull_request.merged:
    - name: 合并后mac平台构建
      git:
        enable: true
        submodules:
          enable: true
      wework:
        silentStage: true
      runner: *MAC_RUNNER
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        ARM_OUTPUT_NAME: CodeBuddy-darwin-arm64-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
        X64_OUTPUT_NAME: CodeBuddy-darwin-x64-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
        BUILD_X64: true
      stages:
        - *STAGE_MAC_ARM_INIT
        - *STAGE_MAC_x64_INIT
        - *STAGE_VERSION_MAC
        - *STAGE_TIMESTAMP
        - *STAGE_MAC_ARM_BUILD
        - *STAGE_MAC_x64_BUILD
$:
  tag_push:
    - name: 创建tag mac平台构建
      git:
        enable: true
        submodules:
          enable: true
      wework:
        silentStage: true
      runner: *MAC_RUNNER
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        ARM_OUTPUT_NAME: CodeBuddy-darwin-arm64-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
        X64_OUTPUT_NAME: CodeBuddy-darwin-x64-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
        BUILD_X64: true
      stages:
        - *STAGE_MAC_ARM_INIT
        - *STAGE_MAC_x64_INIT
        - *STAGE_VERSION_MAC
        - *STAGE_TIMESTAMP
        - *STAGE_MAC_ARM_BUILD
        - *STAGE_MAC_x64_BUILD
