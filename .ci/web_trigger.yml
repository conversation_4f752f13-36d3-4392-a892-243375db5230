(master|feat/frontend-cicd|release/*):
  web_trigger_ide:
    - name: IDE构建 - Mac ARM
      runner: &MAC_RUNNER
        tags:
          - cnb:arch:custom
          - cnb:aicopilot:mac
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        BUILD_X64: false
        ARM_OUTPUT_NAME: CodeBuddy-darwin-arm64-${PACKAGE_VERSION}-${CNB_COMMIT_SHORT}
      stages:
        - name: 准备
          script: |
            echo "构建平台: $platform"
            echo "分支: $CNB_BRANCH"
            echo "提交: $CNB_COMMIT"

            if [ "$platform" != "mac-arm" ]; then
              echo "跳过 Mac ARM 构建"
              exit 0
            fi
        - name: 获取COS_PATH_MAC_ARM
          script: echo /web/aiide/darwin-arm64/
          exports:
            info: COS_PATH_MAC_ARM
        - name: 获取版本号
          script:
            - echo $(cat product.json | grep -o '"genieVersion"\s*:\s*"[^"]*"' | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+')
          exports:
            info: PACKAGE_VERSION

        - name: mac arm构建
          jobs:
            - name: mac arm构建
              timeout: 1h
              script: |
                sh build_macos_min.sh arm64 output-name=$ARM_OUTPUT_NAME
            - name: 查看产物
              script: ls ./result/
            - name: 上传arm zip产物
              script: |
                coscmd upload ./result/$ARM_OUTPUT_NAME.zip $COS_PATH_MAC_ARM
            - name: 输出arm zip产物地址
              script:
                - echo $COS_PATH_PREFIX$COS_PATH_MAC_ARM$ARM_OUTPUT_NAME.zip
            - name: 上传arm dmg产物
              script: |
                coscmd upload ./result/$ARM_OUTPUT_NAME.dmg $COS_PATH_MAC_ARM
            - name: 输出arm dmg产物地址
              script:
                - echo $COS_PATH_PREFIX$COS_PATH_MAC_ARM$ARM_OUTPUT_NAME.dmg

    - name: IDE构建 - Mac X64
      runner: *MAC_RUNNER
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        BUILD_X64: true
        X64_OUTPUT_NAME: CodeBuddy-darwin-x64-${PACKAGE_VERSION}-${CNB_COMMIT_SHORT}
      stages:
        - name: 准备
          script: |
            echo "构建平台: $platform"
            echo "分支: $CNB_BRANCH"
            echo "提交: $CNB_COMMIT"

            if [ "$platform" != "mac-x64" ]; then
              echo "跳过 Mac X64 构建"
              exit 0
            fi
        - name: 获取COS_PATH_MAC_x64
          script: echo /web/aiide/darwin/
          exports:
            info: COS_PATH_MAC_x64
        - name: 获取版本号
          script:
            - echo $(cat product.json | grep -o '"genieVersion"\s*:\s*"[^"]*"' | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+')
          exports:
            info: PACKAGE_VERSION
        - name: mac x64构建
          jobs:
            - name: mac x64构建
              timeout: 1h
              script: |
                sh build_macos_min.sh x64 output-name=$X64_OUTPUT_NAME
            - name: 查看产物
              script: ls ./result/
            - name: 上传X64 zip产物
              script: |
                coscmd upload ./result/$X64_OUTPUT_NAME.zip $COS_PATH_MAC_x64
            - name: 输出X64 zip产物地址
              script:
                - echo $COS_PATH_PREFIX$COS_PATH_MAC_x64$X64_OUTPUT_NAME.zip
            - name: 上传X64 dmg产物
              script: |
                coscmd upload ./result/$X64_OUTPUT_NAME.dmg $COS_PATH_MAC_x64
            - name: 输出X64 dmg产物地址
              script:
                - echo $COS_PATH_PREFIX$COS_PATH_MAC_x64$X64_OUTPUT_NAME.dmg

    - name: IDE构建 - Windows X64
      runner: &WINDOWS_RUNNER
        tags:
          - cnb:arch:custom
          - cnb:aicopilot:windows
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        OUTPUT_NAME: CodeBuddy-win32-x64-${PACKAGE_VERSION}-${CNB_COMMIT_SHORT}
      stages:
        - name: 准备
          script: |
            echo "构建平台: $platform"
            echo "分支: $CNB_BRANCH"
            echo "提交: $CNB_COMMIT"

            if [ "$platform" != "win-x64" ]; then
              echo "跳过 Windows X64 构建"
              exit 0
            fi
        - name: 获取版本号
          script:
            - powershell -Command "(Get-Content product.json | ConvertFrom-Json).genieVersion"
          exports:
            info: PACKAGE_VERSION
        - name: windows x64名称
          script: echo CodeBuddy-win32-x64-${PACKAGE_VERSION}-${CNB_COMMIT_SHORT}
          exports:
            info: OUTPUT_NAME
        - name: windows x64构建
          jobs:
            - name: 构建
              timeout: 1h
              # retry: 1
              script: |
                .\build_win_min.bat x64 output-name=$OUTPUT_NAME --sign
        - name: 获取COS_PATH
          script: echo /web/aiide/win32-64-archive/
          exports:
            info: COS_PATH
        - name: 构建产物上传到cos
          script: |
            coscmd upload -r ./result/ $env:COS_PATH
            echo $env:COS_PATH
        - name: 输出产物地址
          script:
            - echo $env:COS_PATH_PREFIX$env:COS_PATH$env:OUTPUT_NAME
