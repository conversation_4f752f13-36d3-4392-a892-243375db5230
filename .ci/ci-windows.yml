WINDOWS_RUNNER: &WINDOWS_RUNNER
  tags:
    - cnb:arch:custom
    - cnb:aicopilot:windows

STAGE_WINDOWS_X64_BUILD: &STAGE_WINDOWS_X64_BUILD
  - name: windows x64构建
    jobs:
      - name: 构建
        timeout: 1h
        retry: 1
        script: |
          .\build_win_min.bat x64 output-name=$env:OUTPUT_NAME --sign

STAGE_WINDOWS_X64_OUTPUT: &STAGE_WINDOWS_X64_OUTPUT
  - name: 获取COS_PATH
    script: echo /web/aiide/win32-64-archive/
    exports:
      info: COS_PATH
  - name: 构建产物上传到cos
    script: |
      coscmd upload -r ./result/ $env:COS_PATH
      echo $env:COS_PATH
  - name: 输出产物cos地址
    script: |
      Get-ChildItem ".\result\*" -File | ForEach-Object { Write-Host "产物地址:$($env:COS_PATH_PREFIX)$($env:COS_PATH)$($_.Name)" }
  - name: 上传产物到附件
    script: |
      $env:CNB_TOKEN="8brVIveMZ1cc0ZUOdeifzAaO0Se";$CNB_COMMIT = $env:CNB_COMMIT; Get-ChildItem ".\result\*" -File | ForEach-Object { Write-Host "正在上传$($_.Name)"; git-cnb commit asset-upload -f $_.FullName -s $CNB_COMMIT }

STAGE_WINDOWS_X64_CI: &STAGE_WINDOWS_X64_CI
  - name: windows x64编译通过性检测
    jobs:
      - name: 检查
        timeout: 1h
        retry: 1
        script:
          - .\build_win_ci.bat x64

STAGE_WINDOWS_NAME: &STAGE_WINDOWS_NAME
  - name: windows x64名称
    script: echo CodeBuddyIDE-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
    exports:
      info: OUTPUT_NAME

STAGE_WINDOWS_INIT: &STAGE_WINDOWS_INIT
  - name: windows初始化
    jobs:
      - name: submodule
        script: |
          powershell Remove-Item -Path ".\result\*" -Recurse -Force
          powershell Remove-Item -Recurse -Force "$env:LOCALAPPDATA\node-gyp\Cache"
          git submodule init
          git submodule update --remote --force
        allowFailure: true
      - name: node_modules
        script: |
          powershell -File C:\etc\restore-modules.ps1
        allowFailure: true
STAGE_VERSION_WIN: &STAGE_VERSION_WIN
  - name: version
    script:
      - powershell -Command "(Get-Content product.json | ConvertFrom-Json).genieVersion"
    exports:
      info: PACKAGE_VERSION

STAGE_TIMESTAMP: &STAGE_TIMESTAMP
  - name: timestamp
    script: |
      # 获取当前时间戳（秒）
      $NOW = [int][double]::Parse((Get-Date -Date (Get-Date) -UFormat %s))

      # 设置基准时间（注意格式必须一致）
      $BASELINE_TIME = [datetime]::ParseExact("2025-06-24 00:00:00", "yyyy-MM-dd HH:mm:ss", $null)
      $BASELINE = [int][double]::Parse((Get-Date -Date $BASELINE_TIME -UFormat %s))
      Write-Host "$($NOW - $BASELINE)"
    exports:
      info: TIMESTAMP

master:
  # pull_request:
    # - name: windows 平台检查
    #   git:
    #     enable: true
    #     submodules:
    #       enable: true
    #   wework:
    #     silentStage: true
    #   runner: *WINDOWS_RUNNER
    #   stages:
    #     - *STAGE_WINDOWS_INIT
    #     - *STAGE_WINDOWS_X64_CI

  pull_request.merged:
    - name: 合并后windows平台构建
      git:
        enable: true
        submodules:
          enable: true
      wework:
        silentStage: true
      runner: *WINDOWS_RUNNER
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        OUTPUT_NAME: CodeBuddy-win32-x64-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
      stages:
        - *STAGE_WINDOWS_INIT
        - *STAGE_VERSION_WIN
        - *STAGE_TIMESTAMP
        - *STAGE_WINDOWS_X64_BUILD
        - *STAGE_WINDOWS_X64_OUTPUT
$:
  tag_push:
    - name: 创建tag windows平台构建
      git:
        enable: true
        submodules:
          enable: true
      wework:
        silentStage: true
      runner: *WINDOWS_RUNNER
      env:
        COS_PATH_PREFIX: https://acc-1258344699.cos.ap-guangzhou.myqcloud.com
        OUTPUT_NAME: CodeBuddy-win32-x64-${PACKAGE_VERSION}.${TIMESTAMP}-${CNB_COMMIT_SHORT}
      stages:
        - *STAGE_VERSION_WIN
        - *STAGE_TIMESTAMP
        - *STAGE_WINDOWS_NAME
        - *STAGE_WINDOWS_X64_BUILD
        - *STAGE_WINDOWS_X64_OUTPUT
