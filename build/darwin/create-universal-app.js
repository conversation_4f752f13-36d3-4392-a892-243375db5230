"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const minimatch_1 = __importDefault(require("minimatch"));
const vscode_universal_bundler_1 = require("vscode-universal-bundler");
// const root = path.dirname(path.dirname(__dirname));
async function main(buildDir) {
    // const arch = process.env['VSCODE_ARCH'];
    if (!buildDir) {
        throw new Error('Build dir not provided');
    }
    const appName = 'CodeBuddy.app';
    const x64AppPath = path_1.default.join(buildDir, 'x64', appName);
    const arm64AppPath = path_1.default.join(buildDir, 'arm64', appName);
    const asarRelativePath = path_1.default.join('Contents', 'Resources', 'app', 'node_modules.asar');
    const outAppPath = path_1.default.join(buildDir, 'universal', appName);
    const productJsonPath = path_1.default.resolve(outAppPath, 'Contents', 'Resources', 'app', 'product.json');
    const filesToSkip = [
        '**/CodeResources',
        '**/Credits.rtf',
        '**/policies/{*.mobileconfig,**/*.plist}',
        // 跳过 product.json 文件比较，因为它们在不同架构版本之间可能有差异
        '**/product.json',
        // TODO: Should we consider expanding this to other files in this area?
        '**/node_modules/@parcel/node-addon-api/nothing.target.mk'
    ];
    await (0, vscode_universal_bundler_1.makeUniversalApp)({
        x64AppPath,
        arm64AppPath,
        asarPath: asarRelativePath,
        outAppPath,
        force: true,
        mergeASARs: true,
        x64ArchFiles: '*/kerberos.node',
        filesToSkipComparison: (file) => {
            for (const expected of filesToSkip) {
                if ((0, minimatch_1.default)(file, expected)) {
                    return true;
                }
            }
            return false;
        }
    });
    const productJson = JSON.parse(fs_1.default.readFileSync(productJsonPath, 'utf8'));
    Object.assign(productJson, {
        darwinUniversalAssetId: 'darwin-universal'
    });
    fs_1.default.writeFileSync(productJsonPath, JSON.stringify(productJson, null, '\t'));
}
if (require.main === module) {
    main(process.argv[2]).catch(err => {
        console.error(err);
        process.exit(1);
    });
}
//# sourceMappingURL=create-universal-app.js.map