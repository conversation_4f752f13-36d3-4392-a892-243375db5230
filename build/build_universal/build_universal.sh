#!/bin/bash
# ==============================================================================
# CodeBuddy IDE macOS Universal 应用构建脚本
#
# 功能描述：本脚本用于自动化构建 CodeBuddy IDE 的 macOS Universal 应用包
#
# 使用方式：./build_universal.sh [version=版本号]
#
# 参数说明：
#   version     - 可选参数，指定版本号，格式为 version=版本号，默认为dev
#
# 环境要求：
#   - Node.js v20.18.1（推荐通过 nvm 管理）
#   - npm/yarn 包管理器
#   - git 及子模块支持
#   - Xcode 命令行工具（用于代码签名）
#
# ==============================================================================

# 设置出错时退出脚本
set -e

# 获取脚本所在目录的绝对路径
ORIGINAL_SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SCRIPT_DIR="$ORIGINAL_SCRIPT_DIR"
# 获取项目根目录
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# 读取版本号
VERSION="dev"

# 检查是否通过参数指定了版本号
for arg in "$@"; do
    if [[ $arg == version=* ]]; then
        VERSION="${arg#version=}"
    fi
done

echo "使用版本号: $VERSION"

# 获取时间戳
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
echo "当前构建时间戳: $TIMESTAMP"

# 设置环境变量
export VSCODE_ARCH=universal

# 定义构建目录
BUILD_DIR="$PROJECT_ROOT"
RESULT_DIR="$PROJECT_ROOT/result"

# 确保结果目录存在
mkdir -p "$RESULT_DIR"

# 定义应用名称
APP_NAME="CodeBuddy.app"

# 定义输出路径
X64_APP_PATH="$SCRIPT_DIR/x64/$APP_NAME"
ARM64_APP_PATH="$SCRIPT_DIR/arm64/$APP_NAME"
UNIVERSAL_APP_PATH="$SCRIPT_DIR/universal/$APP_NAME"

# 打印带标题的日志函数
log_section() {
    echo ""
    echo "==============================================="
    echo "  $1"
    echo "==============================================="
}

log_info() {
    echo "[INFO] $1"
}

log_success() {
    echo "[SUCCESS] $1"
}

log_warning() {
    echo "[WARNING] $1"
}

log_error() {
    echo "[ERROR] $1"
}

# 使用已有的 x64 和 arm64 版本
log_section "使用已有的 x64 和 arm64 版本"
log_info "x64 版本路径: $SCRIPT_DIR/x64/$APP_NAME"
log_info "arm64 版本路径: $SCRIPT_DIR/arm64/$APP_NAME"

# 检查两个架构的应用是否都存在
if [ ! -d "$SCRIPT_DIR/x64/$APP_NAME" ]; then
    log_error "x64 应用不存在: $SCRIPT_DIR/x64/$APP_NAME"
    exit 1
fi

if [ ! -d "$SCRIPT_DIR/arm64/$APP_NAME" ]; then
    log_error "arm64 应用不存在: $SCRIPT_DIR/arm64/$APP_NAME"
    exit 1
fi

# 删除之前的 universal 包（如果存在）
log_section "清理之前的 Universal 应用"
if [ -d "$SCRIPT_DIR/universal" ]; then
    log_info "删除之前的 Universal 应用目录: $SCRIPT_DIR/universal"
    rm -rf "$SCRIPT_DIR/universal"
    log_success "成功删除之前的 Universal 应用目录"
else
    log_info "没有找到之前的 Universal 应用目录，无需清理"
fi

# 创建 Universal 应用
log_section "创建 Universal 应用"
cd "$PROJECT_ROOT"

# 编译 TypeScript 文件（如果需要）
if [ ! -f "$PROJECT_ROOT/build/darwin/create-universal-app.js" ]; then
    log_info "编译 TypeScript 文件"
    yarn update-build-script || {
        log_error "TypeScript 编译失败"
        exit 1
    }
fi

# 运行合并脚本
log_info "运行 create-universal-app.js 脚本"
node "$PROJECT_ROOT/build/darwin/create-universal-app.js" "$SCRIPT_DIR" || {
    log_error "Universal 应用创建失败"
    exit 1
}

# 检查 Universal 应用是否创建成功
if [ -d "$UNIVERSAL_APP_PATH" ]; then
    log_success "Universal 应用创建成功: $UNIVERSAL_APP_PATH"

    # 对 Universal 应用进行签名
    log_section "对 Universal 应用进行签名"
    source "$PROJECT_ROOT/build/common/sign_app.sh"
    signApp "$UNIVERSAL_APP_PATH" || {
        log_warning "应用签名失败，但将继续构建过程"
    }
    log_success "Universal 应用签名完成"



    # 创建 ZIP 包
    log_section "创建 ZIP 包"
    PACKAGE_NAME="CodeBuddyIDE-universal-${VERSION}-${TIMESTAMP}.zip"
    OUTPUT_ZIP="${RESULT_DIR}/${PACKAGE_NAME}"

    # 定义 universal 目录的路径，使用原始脚本目录
    UNIVERSAL_DIR="$ORIGINAL_SCRIPT_DIR/universal"
    echo "Universal 目录: $UNIVERSAL_DIR"

    # 检查 universal 目录是否存在
    if [ ! -d "$UNIVERSAL_DIR" ]; then
        log_error "Universal 目录不存在: $UNIVERSAL_DIR"
        exit 1
    fi

    # 使用绝对路径创建 ZIP 包
    log_info "从目录 $UNIVERSAL_DIR 创建 ZIP 包到 $OUTPUT_ZIP"
    ditto -c -k --sequesterRsrc --keepParent "$UNIVERSAL_DIR/$APP_NAME" "$OUTPUT_ZIP" || {
        log_warning "ZIP 包创建失败（非致命错误）"
    }

    if [ -f "$OUTPUT_ZIP" ]; then
        log_success "ZIP 包创建成功: $OUTPUT_ZIP"
        ls -lh "$OUTPUT_ZIP"
        
        # 对 ZIP 包进行公证
        log_section "对 ZIP 包进行公证"
        log_info "开始公证 ZIP 包: $OUTPUT_ZIP"
        
        # 使用 Apple 开发者账号进行公证
        xcrun notarytool submit "$OUTPUT_ZIP" \
            --apple-id "$APPLE_ACCOUNT" \
            --password "$APPLE_PASSWORD" \
            --team-id "$APPLE_TEAM_ID" \
            --wait || {
            log_warning "ZIP 包公证失败，但将继续构建过程"
        }
        
        # 将公证结果附加到 ZIP 包上
        xcrun stapler staple "$OUTPUT_ZIP" || {
            log_warning "附加公证结果失败，但将继续构建过程"
        }
        
        log_success "ZIP 包公证完成"
    fi
else
    log_error "Universal 应用创建失败，目录不存在: $UNIVERSAL_APP_PATH"
    exit 1
fi

log_section "构建完成"
echo "Universal 应用路径: $UNIVERSAL_APP_PATH"
echo "ZIP 包路径: $OUTPUT_ZIP"
