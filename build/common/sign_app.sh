#!/bin/bash
#############################
# 应用签名脚本
# 该脚本用于签名macOS应用程序及其组件
# 由build_dmg.sh和build_zip.sh共用
#############################

#############################变量############################
# 当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 项目根目录
WORKSPACE="$(cd "$SCRIPT_DIR/../.." && pwd)"
# 资源目录
RESOURCES_PATH="$SCRIPT_DIR/resources"

#############################常量############################
# 开发者账号信息
APPLE_KEY_P12="Developer ID Application: Tencent Technology (Shanghai) Co., Ltd (FN2V63AD2J)"
APPLE_ACCOUNT=<EMAIL>
APPLE_PASSWORD=lydo-ntxo-mmft-whtm
APPLE_TEAM_ID=FN2V63AD2J

#############################函数############################

# 签名额外的组件和文件
signComponents() {
    echo "开始签名额外的组件和文件..."
    local app_base="$1"
    local components=(
        "Contents/Frameworks/CodeBuddy Helper (GPU).app/Contents/MacOS/CodeBuddy Helper (GPU)"
        "Contents/Frameworks/CodeBuddy Helper (Plugin).app/Contents/MacOS/CodeBuddy Helper (Plugin)"
        "Contents/Frameworks/CodeBuddy Helper (Renderer).app/Contents/MacOS/CodeBuddy Helper (Renderer)"
        "Contents/Frameworks/CodeBuddy Helper.app/Contents/MacOS/CodeBuddy Helper"
        "Contents/Frameworks/CodeBuddy Helper (GPU).app"
        "Contents/Frameworks/CodeBuddy Helper (Plugin).app"
        "Contents/Frameworks/CodeBuddy Helper (Renderer).app"
        "Contents/Frameworks/CodeBuddy Helper.app"
    )
    local app_entitlements="$RESOURCES_PATH/app-entitlements.plist"
    local plugin_entitlements="$RESOURCES_PATH/helper-plugin-entitlements.plist"
    local renderer_entitlements="$RESOURCES_PATH/helper-renderer-entitlements.plist"
    local gpu_entitlements="$RESOURCES_PATH/helper-gpu-entitlements.plist"

    for component in "${components[@]}"; do
        local full_path="$app_base/$component"
        if [ ! -e "$full_path" ]; then
            echo "警告：组件不存在，跳过: $full_path"
            continue
        fi

        # 根据组件类型选择对应的entitlements文件
        local entitlements_file="$app_entitlements"
        if [[ "$component" == *"Plugin"* ]]; then
            entitlements_file="$plugin_entitlements"
        elif [[ "$component" == *"Renderer"* ]]; then
            entitlements_file="$renderer_entitlements"
        elif [[ "$component" == *"GPU"* ]]; then
            entitlements_file="$gpu_entitlements"
        fi

        # 检查entitlements文件是否存在
        if [ ! -f "$entitlements_file" ]; then
            echo "警告：entitlements文件不存在: $entitlements_file，使用默认签名"
            codesign --force --options runtime --timestamp --sign "$APPLE_KEY_P12" --verbose "$full_path"
        else
            codesign --force --options runtime --timestamp --entitlements "$entitlements_file" --sign "$APPLE_KEY_P12" --verbose "$full_path"
        fi

        # 验证签名
        if ! codesign --verify --verbose=2 "$full_path"; then
            echo "警告：组件签名验证失败: $full_path，尝试重签"
            if [ ! -f "$entitlements_file" ]; then
                codesign --force --options runtime --timestamp --sign "$APPLE_KEY_P12" --verbose "$full_path"
            else
                codesign --force --options runtime --timestamp --entitlements "$entitlements_file" --sign "$APPLE_KEY_P12" --verbose "$full_path"
            fi
        else
            echo "✓ 组件签名验证成功: $full_path"
        fi
    done
}

# 处理有问题的核心组件
signProblemComponents() {
    echo "开始专门处理有问题的核心组件..."
    local app_base="$1"
    local problem_files=(
        "Contents/Frameworks/CodeBuddy Helper (GPU).app/Contents/MacOS/CodeBuddy Helper (GPU)"
        "Contents/Frameworks/CodeBuddy Helper (Plugin).app/Contents/MacOS/CodeBuddy Helper (Plugin)"
        "Contents/Frameworks/CodeBuddy Helper (Renderer).app/Contents/MacOS/CodeBuddy Helper (Renderer)"
        "Contents/Frameworks/CodeBuddy Helper.app/Contents/MacOS/CodeBuddy Helper"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Electron Framework"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libEGL.dylib"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libGLESv2.dylib"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libffmpeg.dylib"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libswiftshader_libEGL.dylib"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libswiftshader_libGLESv2.dylib"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libvk_swiftshader.dylib"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/icudtl.dat"
        "Contents/Frameworks/Electron Framework.framework/Versions/A/Resources/v8_context_snapshot.bin"
        "Contents/Frameworks/ReactiveObjC.framework/Versions/A/ReactiveObjC"
        "Contents/Frameworks/Squirrel.framework/Versions/A/Squirrel"
        "Contents/Frameworks/Mantle.framework/Versions/A/Mantle"
        # 添加公证失败中提到的文件
        "Contents/Frameworks/Squirrel.framework/Versions/A/Resources/ShipIt"
        "Contents/Resources/app/node_modules/@parcel/watcher/build/Release/watcher.node"
        "Contents/Resources/app/node_modules/@vscode/deviceid/build/Release/windows.node"
        "Contents/Resources/app/node_modules/@vscode/policy-watcher/build/Release/vscode-policy-watcher.node"
        "Contents/Resources/app/node_modules/@vscode/ripgrep/bin/rg"
        "Contents/Resources/app/node_modules/@vscode/spdlog/build/Release/spdlog.node"
        "Contents/Resources/app/node_modules/@vscode/sqlite3/build/Release/vscode-sqlite3.node"
        "Contents/Resources/app/node_modules/kerberos/build/Release/kerberos.node"
        "Contents/Resources/app/node_modules/native-is-elevated/build/Release/iselevated.node"
        "Contents/Resources/app/node_modules/native-keymap/build/Release/keymapping.node"
        "Contents/Resources/app/node_modules/native-watchdog/build/Release/watchdog.node"
        "Contents/Resources/app/node_modules/node-pty/build/Release/pty.node"
        "Contents/Resources/app/node_modules/node-pty/build/Release/spawn-helper"
        "Contents/Resources/app/node_modules/windows-foreground-love/build/Release/foreground_love.node"
        # 添加 @img/sharp 相关文件
        "Contents/Resources/app/node_modules/@img/sharp-darwin-arm64/lib/sharp-darwin-arm64.node"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/libvips-cpp.8.16.1.dylib"
        # 添加其他可能的 @img/sharp 相关文件
        "Contents/Resources/app/node_modules/@img/sharp-darwin-arm64/lib/*.node"
        "Contents/Resources/app/node_modules/@img/sharp-darwin-arm64/lib/*.dylib"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/*.dylib"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/*.so"
        # 明确指定可能的 @img/sharp 相关库文件
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/libvips-cpp.42.dylib"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/libvips.42.dylib"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/libglib-2.0.0.dylib"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/libgobject-2.0.0.dylib"
        "Contents/Resources/app/node_modules/@img/sharp-libvips-darwin-arm64/lib/libvips-cpp.8.dylib"
    )
    local app_entitlements="$RESOURCES_PATH/app-entitlements.plist"

    # 添加通配符处理函数
    processWildcardPaths() {
        local base_path="$1"
        local pattern="$2"

        # 检查基础路径是否存在
        local dir_path=$(dirname "$base_path/$pattern")
        if [ ! -d "$dir_path" ]; then
            echo "警告：目录不存在，跳过: $dir_path"
            return
        fi

        # 获取通配符部分
        local wildcard_part=$(basename "$pattern")
        
        # 根据通配符类型使用不同的查找方式
        local files
        if [[ "$wildcard_part" == "*.node" ]]; then
            files=$(find "$dir_path" -name "*.node" 2>/dev/null)
        elif [[ "$wildcard_part" == "*.dylib" ]]; then
            files=$(find "$dir_path" -name "*.dylib" 2>/dev/null)
        elif [[ "$wildcard_part" == "*.so" ]]; then
            files=$(find "$dir_path" -name "*.so" 2>/dev/null)
        else
            # 默认查找方式
            files=$(find "$dir_path" -name "$wildcard_part" 2>/dev/null)
        fi
        
        echo "找到以下匹配文件:"
        echo "$files"
        
        for file in $files; do
            # 检查文件是否存在
            if [ ! -e "$file" ]; then
                echo "警告：文件不存在，跳过: $file"
                continue
            fi

            echo "处理通配符匹配的文件: $file"

            # 检查entitlements文件是否存在
            if [ ! -f "$app_entitlements" ]; then
                echo "警告：entitlements文件不存在: $app_entitlements，使用默认签名"
                codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --sign "$APPLE_KEY_P12" --verbose "$file"
            else
                codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --entitlements "$app_entitlements" --sign "$APPLE_KEY_P12" --verbose "$file"
            fi

            # 验证签名
            if ! codesign --verify --verbose=2 "$file"; then
                echo "警告：文件签名验证失败: $file，尝试重签"
                if [ ! -f "$app_entitlements" ]; then
                    codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --sign "$APPLE_KEY_P12" --verbose "$file"
                else
                    codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --entitlements "$app_entitlements" --sign "$APPLE_KEY_P12" --verbose "$file"
                fi
            else
                echo "✓ 文件签名验证成功: $file"
            fi
        done
    }

    for file in "${problem_files[@]}"; do
        # 检查是否是通配符路径
        if [[ "$file" == *"*"* ]]; then
            processWildcardPaths "$app_base" "$file"
            continue
        fi

        local full_path="$app_base/$file"
        if [ ! -e "$full_path" ]; then
            echo "警告：文件不存在，跳过: $full_path"
            continue
        fi

        # 检查entitlements文件是否存在
        if [ ! -f "$app_entitlements" ]; then
            echo "警告：entitlements文件不存在: $app_entitlements，使用默认签名"
            codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --sign "$APPLE_KEY_P12" --verbose "$full_path"
        else
            codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --entitlements "$app_entitlements" --sign "$APPLE_KEY_P12" --verbose "$full_path"
        fi

        # 验证签名
        if ! codesign --verify --verbose=2 "$full_path"; then
            echo "警告：文件签名验证失败: $full_path，尝试重签"
            if [ ! -f "$app_entitlements" ]; then
                codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --sign "$APPLE_KEY_P12" --verbose "$full_path"
            else
                codesign --force --deep --options runtime --timestamp --preserve-metadata=identifier,entitlements --entitlements "$app_entitlements" --sign "$APPLE_KEY_P12" --verbose "$full_path"
            fi
        else
            echo "✓ 文件签名验证成功: $full_path"
        fi
    done

    # 最后签名主应用
    local main_app="$app_base"
    local app_entitlements="$RESOURCES_PATH/app-entitlements.plist"

    # 检查entitlements文件是否存在
    if [ ! -f "$app_entitlements" ]; then
        echo "警告：entitlements文件不存在: $app_entitlements，使用默认签名"
        codesign --force --deep --options runtime --timestamp --sign "$APPLE_KEY_P12" --verbose "$main_app"
    else
        codesign --force --deep --options runtime --timestamp --entitlements "$app_entitlements" --sign "$APPLE_KEY_P12" --verbose "$main_app"
    fi

    # 验证签名
    if ! codesign --verify --verbose=2 "$main_app"; then
        echo "警告：主应用签名验证失败: $main_app，尝试重签"
        if [ ! -f "$app_entitlements" ]; then
            codesign --force --deep --options runtime --timestamp --sign "$APPLE_KEY_P12" --verbose "$main_app"
        else
            codesign --force --deep --options runtime --timestamp --entitlements "$app_entitlements" --sign "$APPLE_KEY_P12" --verbose "$main_app"
        fi
    else
        echo "✓ 主应用签名验证成功: $main_app"
    fi
}

# 签名应用程序
signApp() {
    local app_path="$1"

    echo "开始签名应用程序: $app_path"

    # 检查应用是否存在
    if [ ! -d "$app_path" ]; then
        echo "错误：应用程序不存在: $app_path"
        return 1
    fi

    # 签名所有组件
    signComponents "$app_path"

    # 处理有问题的核心组件
    signProblemComponents "$app_path"

    echo "应用程序签名完成: $app_path"
    return 0
}

# 导出函数
export -f signComponents
export -f signProblemComponents
export -f signApp
