parameters:
  - name: VSCODE_QUALITY
    type: string
  - name: VSCODE_ARCH
    type: string
  - name: VSCODE_CIBUILD
    type: boolean
  - name: VSCODE_RUN_ELECTRON_TESTS
    type: boolean
    default: false
  - name: VSCODE_RUN_BROWSER_TESTS
    type: boolean
    default: false
  - name: VSCODE_RUN_REMOTE_TESTS
    type: boolean
    default: false
  - name: VSCODE_TEST_ARTIFACT_NAME
    type: string
    default: ""

steps:
  - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
    - checkout: self
      fetchDepth: 1
      retryCountOnTaskFailure: 3

  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - task: UsePythonVersion@0
    inputs:
      versionSpec: "3.x"
      addToPath: true

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - template: ../distro/download-distro.yml@self

    - task: AzureKeyVault@2
      displayName: "Azure Key Vault: Get Secrets"
      inputs:
        azureSubscription: vscode
        KeyVaultName: vscode-build-secrets
        SecretsFilter: "github-distro-mixin-password"

    - task: DownloadPipelineArtifact@2
      inputs:
        artifact: Compilation
        path: $(Build.ArtifactStagingDirectory)
      displayName: Download compilation output

    - task: ExtractFiles@1
      displayName: Extract compilation output
      inputs:
        archiveFilePatterns: "$(Build.ArtifactStagingDirectory)/compilation.tar.gz"
        cleanDestinationFolder: false

  - powershell: node build/setup-npm-registry.js $env:NPM_REGISTRY
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Registry

  - pwsh: |
      mkdir .build -ea 0
      node build/azure-pipelines/common/computeNodeModulesCacheKey.js win32 $(VSCODE_ARCH) $(node -p process.arch) > .build/packagelockhash
    displayName: Prepare node_modules cache key

  - task: Cache@2
    inputs:
      key: '"node_modules" | .build/packagelockhash'
      path: .build/node_modules_cache
      cacheHitVar: NODE_MODULES_RESTORED
    displayName: Restore node_modules cache

  - powershell: 7z.exe x .build/node_modules_cache/cache.7z -aoa
    condition: and(succeeded(), eq(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Extract node_modules cache

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      $ErrorActionPreference = "Stop"
      # Set the private NPM registry to the global npmrc file
      # so that authentication works for subfolders like build/, remote/, extensions/ etc
      # which does not have their own .npmrc file
      exec { npm config set registry "$env:NPM_REGISTRY" }
      $NpmrcPath = (npm config get userconfig)
      echo "##vso[task.setvariable variable=NPMRC_PATH]$NpmrcPath"
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM

  - task: npmAuthenticate@0
    inputs:
      workingFile: $(NPMRC_PATH)
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Authentication

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      $ErrorActionPreference = "Stop"
      exec { npm ci }
    env:
      npm_config_arch: $(VSCODE_ARCH)
      npm_config_foreground_scripts: "true"
      ELECTRON_SKIP_BINARY_DOWNLOAD: 1
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    retryCountOnTaskFailure: 5
    displayName: Install dependencies
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - powershell: node build/azure-pipelines/distro/mixin-npm
      condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))
      displayName: Mixin distro node modules

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      $ErrorActionPreference = "Stop"
      exec { node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt }
      exec { mkdir -Force .build/node_modules_cache }
      exec { 7z.exe a .build/node_modules_cache/cache.7z -mx3 `@.build/node_modules_list.txt }
    condition: and(succeeded(), ne(variables.NODE_MODULES_RESTORED, 'true'))
    displayName: Create node_modules archive

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - powershell: node build/azure-pipelines/distro/mixin-quality
      displayName: Mixin distro quality

  - template: ../common/install-builtin-extensions.yml@self

  - ${{ if and(ne(parameters.VSCODE_CIBUILD, true), ne(parameters.VSCODE_QUALITY, 'oss')) }}:
    - powershell: node build\lib\policies win32
      displayName: Generate Group Policy definitions
      retryCountOnTaskFailure: 3

  - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
    - powershell: npm run gulp "transpile-client-esbuild" "transpile-extensions"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Transpile client and extensions

  - ${{ else }}:
    - ${{ if and(ne(parameters.VSCODE_CIBUILD, true), eq(parameters.VSCODE_QUALITY, 'insider')) }}:
      - powershell: node build/win32/explorer-appx-fetcher .build/win32/appx
        displayName: Download Explorer Sparse Package

    - powershell: |
        . build/azure-pipelines/win32/exec.ps1
        $ErrorActionPreference = "Stop"
        exec { npm run gulp "vscode-win32-$(VSCODE_ARCH)-min-ci" }
        exec { npm run gulp "vscode-win32-$(VSCODE_ARCH)-inno-updater" }
        echo "##vso[task.setvariable variable=BUILT_CLIENT]true"
        echo "##vso[task.setvariable variable=CodeSigningFolderPath]$(Agent.BuildDirectory)/VSCode-win32-$(VSCODE_ARCH)"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Build client

    - powershell: |
        . build/azure-pipelines/win32/exec.ps1
        $ErrorActionPreference = "Stop"
        exec { npm run gulp "vscode-reh-win32-$(VSCODE_ARCH)-min-ci" }
        mv ..\vscode-reh-win32-$(VSCODE_ARCH) ..\vscode-server-win32-$(VSCODE_ARCH) # TODO@joaomoreno
        echo "##vso[task.setvariable variable=BUILT_SERVER]true"
        echo "##vso[task.setvariable variable=CodeSigningFolderPath]$(CodeSigningFolderPath),$(Agent.BuildDirectory)/vscode-server-win32-$(VSCODE_ARCH)"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Build server

    - powershell: |
        . build/azure-pipelines/win32/exec.ps1
        $ErrorActionPreference = "Stop"
        exec { npm run gulp "vscode-reh-web-win32-$(VSCODE_ARCH)-min-ci" }
        mv ..\vscode-reh-web-win32-$(VSCODE_ARCH) ..\vscode-server-win32-$(VSCODE_ARCH)-web # TODO@joaomoreno
        echo "##vso[task.setvariable variable=BUILT_WEB]true"
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Build server (web)

  - ${{ if ne(parameters.VSCODE_CIBUILD, true) }}:
    - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
      - task: DownloadPipelineArtifact@2
        inputs:
          artifact: unsigned_vscode_cli_win32_$(VSCODE_ARCH)_cli
          patterns: "**"
          path: $(Build.ArtifactStagingDirectory)/cli
        displayName: Download VS Code CLI

      - powershell: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          $ArtifactName = (gci -Path "$(Build.ArtifactStagingDirectory)/cli" | Select-Object -last 1).FullName
          Expand-Archive -Path $ArtifactName -DestinationPath "$(Build.ArtifactStagingDirectory)/cli"
          $AppProductJson = Get-Content -Raw -Path "$(Agent.BuildDirectory)\VSCode-win32-$(VSCODE_ARCH)\resources\app\product.json" | ConvertFrom-Json
          $CliAppName = $AppProductJson.tunnelApplicationName
          $AppName = $AppProductJson.applicationName
          Move-Item -Path "$(Build.ArtifactStagingDirectory)/cli/$AppName.exe" -Destination "$(Agent.BuildDirectory)/VSCode-win32-$(VSCODE_ARCH)/bin/$CliAppName.exe"
        displayName: Move VS Code CLI

      - task: UseDotNet@2
        inputs:
          version: 6.x

      - task: EsrpCodeSigning@5
        inputs:
          UseMSIAuthentication: true
          ConnectedServiceName: vscode-esrp
          AppRegistrationClientId: $(ESRP_CLIENT_ID)
          AppRegistrationTenantId: $(ESRP_TENANT_ID)
          AuthAKVName: vscode-esrp
          AuthSignCertName: esrp-sign
          FolderPath: .
          Pattern: noop
        displayName: 'Install ESRP Tooling'

      - powershell: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          $EsrpCodeSigningTool = (gci -directory -filter EsrpCodeSigning_* $(Agent.RootDirectory)\_tasks | Select-Object -last 1).FullName
          $Version = (gci -directory $EsrpCodeSigningTool | Select-Object -last 1).FullName
          echo "##vso[task.setvariable variable=EsrpCliDllPath]$Version\net6.0\esrpcli.dll"
        displayName: Find ESRP CLI

      - powershell: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          exec { npx deemon --detach --wait -- npx zx build/azure-pipelines/win32/codesign.js }
        env:
          SYSTEM_ACCESSTOKEN: $(System.AccessToken)
        displayName: ✍️ Codesign

  - ${{ if or(eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true), eq(parameters.VSCODE_RUN_BROWSER_TESTS, true), eq(parameters.VSCODE_RUN_REMOTE_TESTS, true)) }}:
    - template: product-build-win32-test.yml@self
      parameters:
        VSCODE_QUALITY: ${{ parameters.VSCODE_QUALITY }}
        VSCODE_ARCH: ${{ parameters.VSCODE_ARCH }}
        VSCODE_RUN_ELECTRON_TESTS: ${{ parameters.VSCODE_RUN_ELECTRON_TESTS }}
        VSCODE_RUN_BROWSER_TESTS: ${{ parameters.VSCODE_RUN_BROWSER_TESTS }}
        VSCODE_RUN_REMOTE_TESTS: ${{ parameters.VSCODE_RUN_REMOTE_TESTS }}
        VSCODE_TEST_ARTIFACT_NAME: ${{ parameters.VSCODE_TEST_ARTIFACT_NAME }}
        ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
          PUBLISH_TASK_NAME: 1ES.PublishPipelineArtifact@1

  - ${{ if ne(parameters.VSCODE_CIBUILD, true) }}:
    - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
      - powershell: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          exec { npx deemon --attach -- npx zx build/azure-pipelines/win32/codesign.js }
        condition: succeededOrFailed()
        displayName: "✍️ Post-job: Codesign"

      - powershell: |
          $ErrorActionPreference = "Stop"

          $PackageJson = Get-Content -Raw -Path ..\VSCode-win32-$(VSCODE_ARCH)\resources\app\package.json | ConvertFrom-Json
          $Version = $PackageJson.version

          $ClientArchivePath = ".build\win32-$(VSCODE_ARCH)\VSCode-win32-$(VSCODE_ARCH)-$Version.zip"
          $ServerArchivePath = ".build\win32-$(VSCODE_ARCH)\vscode-server-win32-$(VSCODE_ARCH).zip"
          $WebArchivePath = ".build\win32-$(VSCODE_ARCH)\vscode-server-win32-$(VSCODE_ARCH)-web.zip"

          $SystemSetupPath = ".build\win32-$(VSCODE_ARCH)\system-setup\VSCodeSetup-$(VSCODE_ARCH)-$Version.exe"
          $UserSetupPath = ".build\win32-$(VSCODE_ARCH)\user-setup\VSCodeUserSetup-$(VSCODE_ARCH)-$Version.exe"

          mv .build\win32-$(VSCODE_ARCH)\system-setup\VSCodeSetup.exe $SystemSetupPath
          mv .build\win32-$(VSCODE_ARCH)\user-setup\VSCodeSetup.exe $UserSetupPath

          echo "##vso[task.setvariable variable=CLIENT_PATH]$ClientArchivePath"
          echo "##vso[task.setvariable variable=SERVER_PATH]$ServerArchivePath"
          echo "##vso[task.setvariable variable=WEB_PATH]$WebArchivePath"

          echo "##vso[task.setvariable variable=SYSTEM_SETUP_PATH]$SystemSetupPath"
          echo "##vso[task.setvariable variable=USER_SETUP_PATH]$UserSetupPath"
        condition: succeededOrFailed()
        displayName: Move setup packages

      - powershell: echo "##vso[task.setvariable variable=ARTIFACT_PREFIX]attempt$(System.JobAttempt)_"
        condition: and(succeededOrFailed(), notIn(variables['Agent.JobStatus'], 'Succeeded', 'SucceededWithIssues'))
        displayName: Generate artifact prefix

      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(CLIENT_PATH)
          artifactName: $(ARTIFACT_PREFIX)vscode_client_win32_$(VSCODE_ARCH)_archive
          sbomBuildDropPath: $(Agent.BuildDirectory)/VSCode-win32-$(VSCODE_ARCH)
          sbomPackageName: "VS Code Windows $(VSCODE_ARCH)"
          sbomPackageVersion: $(Build.SourceVersion)
        condition: and(succeededOrFailed(), ne(variables['CLIENT_PATH'], ''))
        displayName: Publish archive

      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(SERVER_PATH)
          artifactName: $(ARTIFACT_PREFIX)vscode_server_win32_$(VSCODE_ARCH)_archive
          sbomBuildDropPath: $(Agent.BuildDirectory)/vscode-server-win32-$(VSCODE_ARCH)
          sbomPackageName: "VS Code Windows $(VSCODE_ARCH) Server"
          sbomPackageVersion: $(Build.SourceVersion)
        condition: and(succeededOrFailed(), ne(variables['SERVER_PATH'], ''))
        displayName: Publish server archive

      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(WEB_PATH)
          artifactName: $(ARTIFACT_PREFIX)vscode_web_win32_$(VSCODE_ARCH)_archive
          sbomBuildDropPath: $(Agent.BuildDirectory)/vscode-server-win32-$(VSCODE_ARCH)-web
          sbomPackageName: "VS Code Windows $(VSCODE_ARCH) Web"
          sbomPackageVersion: $(Build.SourceVersion)
        condition: and(succeededOrFailed(), ne(variables['WEB_PATH'], ''))
        displayName: Publish web server archive

      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(SYSTEM_SETUP_PATH)
          artifactName: $(ARTIFACT_PREFIX)vscode_client_win32_$(VSCODE_ARCH)_setup
          sbomBuildDropPath: $(Agent.BuildDirectory)/VSCode-win32-$(VSCODE_ARCH)
          sbomPackageName: "VS Code Windows $(VSCODE_ARCH) System Setup"
          sbomPackageVersion: $(Build.SourceVersion)
        condition: and(succeededOrFailed(), ne(variables['SYSTEM_SETUP_PATH'], ''))
        displayName: Publish system setup

      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(USER_SETUP_PATH)
          artifactName: $(ARTIFACT_PREFIX)vscode_client_win32_$(VSCODE_ARCH)_user-setup
          sbomBuildDropPath: $(Agent.BuildDirectory)/VSCode-win32-$(VSCODE_ARCH)
          sbomPackageName: "VS Code Windows $(VSCODE_ARCH) User Setup"
          sbomPackageVersion: $(Build.SourceVersion)
        condition: and(succeededOrFailed(), ne(variables['USER_SETUP_PATH'], ''))
        displayName: Publish user setup
