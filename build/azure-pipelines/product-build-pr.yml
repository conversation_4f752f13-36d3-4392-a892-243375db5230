trigger:
  - main
  - release/*

pr:
  branches:
    include: ["main", "release/*"]

variables:
  - name: Codeql.SkipTaskAutoInjection
    value: true
  - name: skipComponentGovernanceDetection
    value: true
  - name: NPM_REGISTRY
    value: "none"
  - name: CARGO_REGISTRY
    value: "none"
  - name: VSCODE_CIBUILD
    value: ${{ in(variables['Build.Reason'], 'IndividualCI', 'BatchedCI') }}
  - name: VSCODE_QUALITY
    value: oss
  - name: VSCODE_STEP_ON_IT
    value: false

stages:
  - ${{ if ne(variables['VSCODE_CIBUILD'], true) }}:
    - stage: Compile
      displayName: Compile & Hygiene
      dependsOn: []
      jobs:
      - job: Compile
        displayName: Compile & Hygiene
        pool: 1es-oss-ubuntu-22.04-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
        steps:
          - template: product-compile.yml@self
            parameters:
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}

    - stage: Test
      displayName: Test
      dependsOn: []
      jobs:
      - job: Linuxx64ElectronTest
        displayName: Linux (Electron)
        pool: 1es-oss-ubuntu-22.04-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
          DISPLAY: ":10"
        steps:
          - template: linux/product-build-linux.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: electron
              VSCODE_RUN_ELECTRON_TESTS: true

      - job: Linuxx64BrowserTest
        displayName: Linux (Browser)
        pool: 1es-oss-ubuntu-22.04-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
          DISPLAY: ":10"
        steps:
          - template: linux/product-build-linux.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: browser
              VSCODE_RUN_BROWSER_TESTS: true

      - job: Linuxx64RemoteTest
        displayName: Linux (Remote)
        pool: 1es-oss-ubuntu-22.04-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
          DISPLAY: ":10"
        steps:
          - template: linux/product-build-linux.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: remote
              VSCODE_RUN_REMOTE_TESTS: true

      - job: LinuxCLI
        displayName: Linux (CLI)
        pool: 1es-oss-ubuntu-22.04-x64
        timeoutInMinutes: 30
        steps:
          - template: cli/test.yml@self

      - job: Windowsx64ElectronTests
        displayName: Windows (Electron)
        pool: 1es-oss-windows-2022-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
        steps:
          - template: win32/product-build-win32.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: electron
              VSCODE_RUN_ELECTRON_TESTS: true

      - job: Windowsx64BrowserTests
        displayName: Windows (Browser)
        pool: 1es-oss-windows-2022-x64
        timeoutInMinutes: 60
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
        steps:
          - template: win32/product-build-win32.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: browser
              VSCODE_RUN_BROWSER_TESTS: true

      - job: Windowsx64RemoteTests
        displayName: Windows (Remote)
        pool: 1es-oss-windows-2022-x64
        timeoutInMinutes: 60
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
        steps:
          - template: win32/product-build-win32.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: remote
              VSCODE_RUN_REMOTE_TESTS: true

      - job: macOSx64ElectronTests
        displayName: macOS (Electron)
        pool:
          vmImage: macOS-14
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
        steps:
          - template: darwin/product-build-darwin.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: electron
              VSCODE_RUN_ELECTRON_TESTS: true

      - job: macOSx64BrowserTests
        displayName: macOS (Browser)
        pool:
          vmImage: macOS-14
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
        steps:
          - template: darwin/product-build-darwin.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: browser
              VSCODE_RUN_BROWSER_TESTS: true

      - job: macOSx64RemoteTests
        displayName: macOS (Remote)
        pool:
          vmImage: macOS-14
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
          NPM_ARCH: x64
        steps:
          - template: darwin/product-build-darwin.yml@self
            parameters:
              VSCODE_ARCH: x64
              VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
              VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
              VSCODE_TEST_ARTIFACT_NAME: remote
              VSCODE_RUN_REMOTE_TESTS: true

  - ${{ if eq(variables['VSCODE_CIBUILD'], true) }}:
    - stage: NodeModuleCache
      jobs:
      - job: Linuxx64MaintainNodeModulesCache
        displayName: Linux (Maintain node_modules cache)
        pool: 1es-oss-ubuntu-22.04-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
        steps:
          - template: oss/product-build-pr-cache-linux.yml@self

      - job: Windowsx64MaintainNodeModulesCache
        displayName: Windows (Maintain node_modules cache)
        pool: 1es-oss-windows-2022-x64
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
        steps:
          - template: oss/product-build-pr-cache-win32.yml@self

      - job: macOSx64MaintainNodeModulesCache
        displayName: macOS (Maintain node_modules cache)
        pool:
          vmImage: macOS-14
        timeoutInMinutes: 30
        variables:
          VSCODE_ARCH: x64
        steps:
          - template: oss/product-build-pr-cache-darwin.yml@self
