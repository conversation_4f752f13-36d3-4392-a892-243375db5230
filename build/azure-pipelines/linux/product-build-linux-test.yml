parameters:
  - name: VSCODE_QUALITY
    type: string
  - name: VSCODE_RUN_ELECTRON_TESTS
    type: boolean
  - name: VSCODE_RUN_BROWSER_TESTS
    type: boolean
  - name: VSCODE_RUN_REMOTE_TESTS
    type: boolean
  - name: VSCODE_TEST_ARTIFACT_NAME
    type: string
  - name: PUBLISH_TASK_NAME
    type: string
    default: PublishPipelineArtifact@0

steps:
  - script: npm exec -- npm-run-all -lp "electron $(VSCODE_ARCH)" "playwright-install"
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Download Electron and Playwright
    retryCountOnTaskFailure: 3

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - script: |
        set -e
        APP_ROOT=$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)
        ELECTRON_ROOT=.build/electron
        sudo chown root $APP_ROOT/chrome-sandbox
        sudo chown root $ELECTRON_ROOT/chrome-sandbox
        sudo chmod 4755 $APP_ROOT/chrome-sandbox
        sudo chmod 4755 $ELECTRON_ROOT/chrome-sandbox
        stat $APP_ROOT/chrome-sandbox
        stat $ELECTRON_ROOT/chrome-sandbox
      displayName: Change setuid helper binary permission

  - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
    - ${{ if eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true) }}:
      - script: ./scripts/test.sh --tfs "Unit Tests"
        env:
          DISPLAY: ":10"
        displayName: 🧪 Run unit tests (Electron)
        timeoutInMinutes: 15
      - script: npm run test-node
        displayName: 🧪 Run unit tests (node.js)
        timeoutInMinutes: 15

    - ${{ if eq(parameters.VSCODE_RUN_BROWSER_TESTS, true) }}:
      - script: npm run test-browser-no-install -- --browser chromium --tfs "Browser Unit Tests"
        env:
          DEBUG: "*browser*"
        displayName: 🧪 Run unit tests (Browser, Chromium)
        timeoutInMinutes: 15

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - ${{ if eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true) }}:
      - script: ./scripts/test.sh --build --tfs "Unit Tests"
        displayName: 🧪 Run unit tests (Electron)
        timeoutInMinutes: 15
      - script: npm run test-node -- --build
        displayName: 🧪 Run unit tests (node.js)
        timeoutInMinutes: 15

    - ${{ if eq(parameters.VSCODE_RUN_BROWSER_TESTS, true) }}:
      - script: npm run test-browser-no-install -- --build --browser chromium --tfs "Browser Unit Tests"
        env:
          DEBUG: "*browser*"
        displayName: 🧪 Run unit tests (Browser, Chromium)
        timeoutInMinutes: 15

  - script: |
      set -e
      npm run gulp \
        compile-extension:configuration-editing \
        compile-extension:css-language-features-server \
        compile-extension:emmet \
        compile-extension:git \
        compile-extension:github-authentication \
        compile-extension:html-language-features-server \
        compile-extension:ipynb \
        compile-extension:notebook-renderers \
        compile-extension:json-language-features-server \
        compile-extension:markdown-language-features \
        compile-extension-media \
        compile-extension:microsoft-authentication \
        compile-extension:typescript-language-features \
        compile-extension:vscode-api-tests \
        compile-extension:vscode-colorize-tests \
        compile-extension:vscode-colorize-perf-tests \
        compile-extension:vscode-test-resolver
    displayName: Build integration tests

  - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
    - ${{ if eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true) }}:
      - script: ./scripts/test-integration.sh --tfs "Integration Tests"
        env:
          DISPLAY: ":10"
        displayName: 🧪 Run integration tests (Electron)
        timeoutInMinutes: 20

    - ${{ if eq(parameters.VSCODE_RUN_BROWSER_TESTS, true) }}:
      - script: ./scripts/test-web-integration.sh --browser chromium
        displayName: 🧪 Run integration tests (Browser, Chromium)
        timeoutInMinutes: 20

    - ${{ if eq(parameters.VSCODE_RUN_REMOTE_TESTS, true) }}:
      - script: ./scripts/test-remote-integration.sh
        displayName: 🧪 Run integration tests (Remote)
        timeoutInMinutes: 20

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - ${{ if eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true) }}:
      - script: |
          # Figure out the full absolute path of the product we just built
          # including the remote server and configure the integration tests
          # to run with these builds instead of running out of sources.
          set -e
          APP_ROOT=$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)
          APP_NAME=$(node -p "require(\"$APP_ROOT/resources/app/product.json\").applicationName")
          INTEGRATION_TEST_APP_NAME="$APP_NAME" \
          INTEGRATION_TEST_ELECTRON_PATH="$APP_ROOT/$APP_NAME" \
          ./scripts/test-integration.sh --build --tfs "Integration Tests"
        env:
          VSCODE_REMOTE_SERVER_PATH: $(agent.builddirectory)/vscode-server-linux-$(VSCODE_ARCH)
        displayName: 🧪 Run integration tests (Electron)
        timeoutInMinutes: 20

    - ${{ if eq(parameters.VSCODE_RUN_BROWSER_TESTS, true) }}:
      - script: ./scripts/test-web-integration.sh --browser chromium
        env:
          VSCODE_REMOTE_SERVER_PATH: $(agent.builddirectory)/vscode-server-linux-$(VSCODE_ARCH)-web
        displayName: 🧪 Run integration tests (Browser, Chromium)
        timeoutInMinutes: 20

    - ${{ if eq(parameters.VSCODE_RUN_REMOTE_TESTS, true) }}:
      - script: |
          set -e
          APP_ROOT=$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)
          APP_NAME=$(node -p "require(\"$APP_ROOT/resources/app/product.json\").applicationName")
          INTEGRATION_TEST_APP_NAME="$APP_NAME" \
          INTEGRATION_TEST_ELECTRON_PATH="$APP_ROOT/$APP_NAME" \
          ./scripts/test-remote-integration.sh
        env:
          VSCODE_REMOTE_SERVER_PATH: $(agent.builddirectory)/vscode-server-linux-$(VSCODE_ARCH)
        displayName: 🧪 Run integration tests (Remote)
        timeoutInMinutes: 20

  - script: |
      set -e
      ps -ef
      cat /proc/sys/fs/inotify/max_user_watches
      lsof | wc -l
    displayName: Diagnostics before smoke test run (processes, max_user_watches, number of opened file handles)
    continueOnError: true
    condition: succeededOrFailed()

  - ${{ if eq(parameters.VSCODE_QUALITY, 'oss') }}:
    - script: npm run compile
      workingDirectory: test/smoke
      displayName: Compile smoke tests

    - script: npm run gulp node
      displayName: Download node.js for remote smoke tests
      retryCountOnTaskFailure: 3

    - ${{ if eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true) }}:
      - script: npm run smoketest-no-compile -- --tracing
        timeoutInMinutes: 20
        displayName: 🧪 Run smoke tests (Electron)

    - ${{ if eq(parameters.VSCODE_RUN_BROWSER_TESTS, true) }}:
      - script: npm run smoketest-no-compile -- --web --tracing --headless
        timeoutInMinutes: 20
        displayName: 🧪 Run smoke tests (Browser, Chromium)

    - ${{ if eq(parameters.VSCODE_RUN_REMOTE_TESTS, true) }}:
      - script: npm run smoketest-no-compile -- --remote --tracing
        timeoutInMinutes: 20
        displayName: 🧪 Run smoke tests (Remote)

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - ${{ if eq(parameters.VSCODE_RUN_ELECTRON_TESTS, true) }}:
      - script: npm run smoketest-no-compile -- --tracing --build "$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)"
        timeoutInMinutes: 20
        displayName: 🧪 Run smoke tests (Electron)

    - ${{ if eq(parameters.VSCODE_RUN_BROWSER_TESTS, true) }}:
      - script: npm run smoketest-no-compile -- --web --tracing --headless
        env:
          VSCODE_REMOTE_SERVER_PATH: $(agent.builddirectory)/vscode-server-linux-$(VSCODE_ARCH)-web
        timeoutInMinutes: 20
        displayName: 🧪 Run smoke tests (Browser, Chromium)

    - ${{ if eq(parameters.VSCODE_RUN_REMOTE_TESTS, true) }}:
      - script: |
          set -e
          APP_PATH=$(agent.builddirectory)/VSCode-linux-$(VSCODE_ARCH)
          VSCODE_REMOTE_SERVER_PATH="$(agent.builddirectory)/vscode-server-linux-$(VSCODE_ARCH)" \
          npm run smoketest-no-compile -- --tracing --remote --build "$APP_PATH"
        timeoutInMinutes: 20
        displayName: 🧪 Run smoke tests (Remote)

  - script: |
      set -e
      ps -ef
      cat /proc/sys/fs/inotify/max_user_watches
      lsof | wc -l
    displayName: Diagnostics after smoke test run (processes, max_user_watches, number of opened file handles)
    continueOnError: true
    condition: succeededOrFailed()

  - task: ${{ parameters.PUBLISH_TASK_NAME }}
    inputs:
      targetPath: .build/crashes
      ${{ if eq(parameters.VSCODE_TEST_ARTIFACT_NAME, '') }}:
        artifactName: crash-dump-linux-$(VSCODE_ARCH)-$(System.JobAttempt)
      ${{ else }}:
        artifactName: crash-dump-linux-$(VSCODE_ARCH)-${{ parameters.VSCODE_TEST_ARTIFACT_NAME }}-$(System.JobAttempt)
      sbomEnabled: false
    displayName: "Publish Crash Reports"
    continueOnError: true
    condition: failed()

  # In order to properly symbolify above crash reports
  # (if any), we need the compiled native modules too
  - task: ${{ parameters.PUBLISH_TASK_NAME }}
    inputs:
      targetPath: node_modules
      ${{ if eq(parameters.VSCODE_TEST_ARTIFACT_NAME, '') }}:
        artifactName: node-modules-linux-$(VSCODE_ARCH)-$(System.JobAttempt)
      ${{ else }}:
        artifactName: node-modules-linux-$(VSCODE_ARCH)-${{ parameters.VSCODE_TEST_ARTIFACT_NAME }}-$(System.JobAttempt)
      sbomEnabled: false
    displayName: "Publish Node Modules"
    continueOnError: true
    condition: failed()

  - task: ${{ parameters.PUBLISH_TASK_NAME }}
    inputs:
      targetPath: .build/logs
      ${{ if eq(parameters.VSCODE_TEST_ARTIFACT_NAME, '') }}:
        artifactName: logs-linux-$(VSCODE_ARCH)-$(System.JobAttempt)
      ${{ else }}:
        artifactName: logs-linux-$(VSCODE_ARCH)-${{ parameters.VSCODE_TEST_ARTIFACT_NAME }}-$(System.JobAttempt)
      sbomEnabled: false
    displayName: "Publish Log Files"
    continueOnError: true
    condition: succeededOrFailed()

  - task: PublishTestResults@2
    displayName: Publish Tests Results
    inputs:
      testResultsFiles: "*-results.xml"
      searchFolder: "$(Build.ArtifactStagingDirectory)/test-results"
    condition: succeededOrFailed()
