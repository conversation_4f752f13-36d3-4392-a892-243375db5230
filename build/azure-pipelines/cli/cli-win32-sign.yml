parameters:
  - name: VSCODE_CLI_ARTIFACTS
    type: object
    default: []

steps:
  - task: UseDotNet@2
    inputs:
      version: 6.x

  - task: EsrpCodeSigning@5
    inputs:
      UseMSIAuthentication: true
      ConnectedServiceName: vscode-esrp
      AppRegistrationClientId: $(ESRP_CLIENT_ID)
      AppRegistrationTenantId: $(ESRP_TENANT_ID)
      AuthAKVName: vscode-esrp
      AuthSignCertName: esrp-sign
      FolderPath: .
      Pattern: noop
    displayName: 'Install ESRP Tooling'

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      $ErrorActionPreference = "Stop"
      $EsrpCodeSigningTool = (gci -directory -filter EsrpCodeSigning_* $(Agent.RootDirectory)\_tasks | Select-Object -last 1).FullName
      $Version = (gci -directory $EsrpCodeSigningTool | Select-Object -last 1).FullName
      echo "##vso[task.setvariable variable=EsrpCliDllPath]$Version\net6.0\esrpcli.dll"
    displayName: Find ESRP CLI

  - ${{ each target in parameters.VSCODE_CLI_ARTIFACTS }}:
      - task: DownloadPipelineArtifact@2
        displayName: Download artifact
        inputs:
          artifact: ${{ target }}
          path: $(Build.ArtifactStagingDirectory)/pkg/${{ target }}

      - task: ExtractFiles@1
        displayName: Extract artifact
        inputs:
          archiveFilePatterns: $(Build.ArtifactStagingDirectory)/pkg/${{ target }}/*.zip
          destinationFolder: $(Build.ArtifactStagingDirectory)/sign/${{ target }}

  - powershell: node build\azure-pipelines\common\sign $env:EsrpCliDllPath sign-windows $(Build.ArtifactStagingDirectory)/sign "*.exe"
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    displayName: ✍️ Codesign

  - ${{ each target in parameters.VSCODE_CLI_ARTIFACTS }}:
      - powershell: |
          $ASSET_ID = "${{ target }}".replace("unsigned_", "");
          echo "##vso[task.setvariable variable=ASSET_ID]$ASSET_ID"
        displayName: Set asset id variable

      - task: ArchiveFiles@2
        displayName: Archive signed files
        inputs:
          rootFolderOrFile: $(Build.ArtifactStagingDirectory)/sign/${{ target }}
          includeRootFolder: false
          archiveType: zip
          archiveFile: $(Build.ArtifactStagingDirectory)/$(ASSET_ID).zip

      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(Build.ArtifactStagingDirectory)/$(ASSET_ID).zip
          artifactName: $(ASSET_ID)
          sbomBuildDropPath: $(Build.ArtifactStagingDirectory)/sign/${{ target }}
          sbomPackageName: "VS Code Windows ${{ target }} CLI"
          sbomPackageVersion: $(Build.SourceVersion)
        displayName: Publish signed artifact with ID $(ASSET_ID)
