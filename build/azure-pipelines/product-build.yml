pr: none

schedules:
  - cron: "0 5 * * Mon-Fri"
    displayName: Mon-Fri at 7:00
    branches:
      include:
        - main

trigger:
  batch: true
  branches:
    include: ["main", "release/*"]

parameters:
  - name: VSCODE_QUALITY
    displayName: Quality
    type: string
    default: insider
    values:
      - exploration
      - insider
      - stable
  - name: NPM_REGISTRY
    displayName: "Custom NPM Registry"
    type: string
    default: 'https://pkgs.dev.azure.com/monacotools/Monaco/_packaging/vscode/npm/registry/'
  - name: CARGO_REGISTRY
    displayName: "Custom Cargo Registry"
    type: string
    default: 'sparse+https://pkgs.dev.azure.com/monacotools/Monaco/_packaging/vscode/Cargo/index/'
  - name: VSCODE_BUILD_WIN32
    displayName: "🎯 Windows x64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_WIN32_ARM64
    displayName: "🎯 Windows arm64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_LINUX
    displayName: "🎯 Linux x64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_LINUX_ARM64
    displayName: "🎯 Linux arm64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_LINUX_ARMHF
    displayName: "🎯 Linux armhf"
    type: boolean
    default: true
  - name: VSCODE_BUILD_ALPINE
    displayName: "🎯 Alpine x64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_ALPINE_ARM64
    displayName: "🎯 Alpine arm64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_MACOS
    displayName: "🎯 macOS x64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_MACOS_ARM64
    displayName: "🎯 macOS arm64"
    type: boolean
    default: true
  - name: VSCODE_BUILD_MACOS_UNIVERSAL
    displayName: "🎯 macOS universal"
    type: boolean
    default: true
  - name: VSCODE_BUILD_WEB
    displayName: "🎯 Web"
    type: boolean
    default: true
  - name: VSCODE_PUBLISH
    displayName: "Publish to builds.code.visualstudio.com"
    type: boolean
    default: true
  - name: VSCODE_RELEASE
    displayName: "Release build if successful"
    type: boolean
    default: false
  - name: VSCODE_COMPILE_ONLY
    displayName: "Run Compile stage exclusively"
    type: boolean
    default: false
  - name: VSCODE_STEP_ON_IT
    displayName: "Skip tests"
    type: boolean
    default: false

variables:
  - name: VSCODE_PRIVATE_BUILD
    value: ${{ ne(variables['Build.Repository.Uri'], 'https://github.com/microsoft/vscode.git') }}
  - name: NPM_REGISTRY
    ${{ if in(variables['Build.Reason'], 'IndividualCI', 'BatchedCI') }}: # disable terrapin when in VSCODE_CIBUILD
      value: none
    ${{ else }}:
      value: ${{ parameters.NPM_REGISTRY }}
  - name: CARGO_REGISTRY
    value: ${{ parameters.CARGO_REGISTRY }}
  - name: VSCODE_QUALITY
    value: ${{ parameters.VSCODE_QUALITY }}
  - name: VSCODE_BUILD_STAGE_WINDOWS
    value: ${{ or(eq(parameters.VSCODE_BUILD_WIN32, true), eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}
  - name: VSCODE_BUILD_STAGE_LINUX
    value: ${{ or(eq(parameters.VSCODE_BUILD_LINUX, true), eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true), eq(parameters.VSCODE_BUILD_LINUX_ARM64, true)) }}
  - name: VSCODE_BUILD_STAGE_ALPINE
    value: ${{ or(eq(parameters.VSCODE_BUILD_ALPINE, true), eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true)) }}
  - name: VSCODE_BUILD_STAGE_MACOS
    value: ${{ or(eq(parameters.VSCODE_BUILD_MACOS, true), eq(parameters.VSCODE_BUILD_MACOS_ARM64, true)) }}
  - name: VSCODE_BUILD_STAGE_WEB
    value: ${{ eq(parameters.VSCODE_BUILD_WEB, true) }}
  - name: VSCODE_CIBUILD
    value: ${{ in(variables['Build.Reason'], 'IndividualCI', 'BatchedCI') }}
  - name: VSCODE_PUBLISH
    value: ${{ and(eq(parameters.VSCODE_PUBLISH, true), eq(variables.VSCODE_CIBUILD, false), eq(parameters.VSCODE_COMPILE_ONLY, false)) }}
  - name: VSCODE_SCHEDULEDBUILD
    value: ${{ eq(variables['Build.Reason'], 'Schedule') }}
  - name: VSCODE_STEP_ON_IT
    value: ${{ eq(parameters.VSCODE_STEP_ON_IT, true) }}
  - name: VSCODE_BUILD_MACOS_UNIVERSAL
    value: ${{ and(eq(parameters.VSCODE_BUILD_MACOS, true), eq(parameters.VSCODE_BUILD_MACOS_ARM64, true), eq(parameters.VSCODE_BUILD_MACOS_UNIVERSAL, true)) }}
  - name: VSCODE_STAGING_BLOB_STORAGE_ACCOUNT_NAME
    value: vscodeesrp
  - name: PRSS_CDN_URL
    value: https://vscode.download.prss.microsoft.com/dbazure/download
  - name: VSCODE_ESRP_SERVICE_CONNECTION_ID
    value: fe07e6ce-6ffb-4df9-8d27-d129523a3f3e
  - name: VSCODE_ESRP_TENANT_ID
    value: 975f013f-7f24-47e8-a7d3-abc4752bf346
  - name: VSCODE_ESRP_CLIENT_ID
    value: 4ac7ed59-b5e9-4f66-9c30-8d1afa72d32d
  - name: ESRP_TENANT_ID
    value: 975f013f-7f24-47e8-a7d3-abc4752bf346
  - name: ESRP_CLIENT_ID
    value: c24324f7-e65f-4c45-8702-ed2d4c35df99
  - name: AZURE_DOCUMENTDB_ENDPOINT
    value: https://vscode.documents.azure.com/
  - name: VSCODE_MIXIN_REPO
    value: microsoft/vscode-distro
  - name: skipComponentGovernanceDetection
    value: true
  - name: ComponentDetection.Timeout
    value: 600
  - name: Codeql.SkipTaskAutoInjection
    value: true
  - name: ARTIFACT_PREFIX
    value: ''

name: "$(Date:yyyyMMdd).$(Rev:r) (${{ parameters.VSCODE_QUALITY }})"

resources:
  pipelines:
    - pipeline: vscode-7pm-kick-off
      source: 'VS Code 7PM Kick-Off'
      trigger: true
  repositories:
    - repository: 1ESPipelines
      type: git
      name: 1ESPipelineTemplates/1ESPipelineTemplates
      ref: refs/tags/release

extends:
  template: v1/1ES.Official.PipelineTemplate.yml@1esPipelines
  parameters:
    sdl:
      tsa:
        enabled: true
        configFile: $(Build.SourcesDirectory)/build/azure-pipelines/config/tsaoptions.json
      binskim:
        analyzeTargetGlob: '+:file|$(Agent.BuildDirectory)/VSCode-*/**/*.exe;+:file|$(Agent.BuildDirectory)/VSCode-*/**/*.node;+:file|$(Agent.BuildDirectory)/VSCode-*/**/*.dll;-:file|$(Build.SourcesDirectory)/.build/**/system-setup/VSCodeSetup*.exe;-:file|$(Build.SourcesDirectory)/.build/**/user-setup/VSCodeUserSetup*.exe'
      codeql:
        runSourceLanguagesInSourceAnalysis: true
        compiled:
          enabled: false
          justificationForDisabling: "CodeQL breaks ESRP CodeSign on macOS (ICM #520035761, githubcustomers/microsoft-codeql-support#198)"
      credscan:
        suppressionsFile: $(Build.SourcesDirectory)/build/azure-pipelines/config/CredScanSuppressions.json
      eslint:
        enabled: true
        enableExclusions: true
        exclusionsFilePath: $(Build.SourcesDirectory)/.eslint-ignore
      sourceAnalysisPool: 1es-windows-2022-x64
      createAdoIssuesForJustificationsForDisablement: false
    containers:
      ubuntu-2004-arm64:
        image: onebranch.azurecr.io/linux/ubuntu-2004-arm64:latest
    stages:
      - stage: Compile
        jobs:
          - job: Compile
            timeoutInMinutes: 90
            pool:
              name: AcesShared
              os: macOS
            steps:
              - template: build/azure-pipelines/product-compile.yml@self
                parameters:
                  VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}

      - ${{ if or(eq(parameters.VSCODE_BUILD_LINUX, true),eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true),eq(parameters.VSCODE_BUILD_LINUX_ARM64, true),eq(parameters.VSCODE_BUILD_ALPINE, true),eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true),eq(parameters.VSCODE_BUILD_MACOS, true),eq(parameters.VSCODE_BUILD_MACOS_ARM64, true),eq(parameters.VSCODE_BUILD_WIN32, true),eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
        - stage: CompileCLI
          dependsOn: []
          jobs:
            - ${{ if eq(parameters.VSCODE_BUILD_LINUX, true) }}:
              - job: CLILinuxX64
                pool:
                  name: 1es-ubuntu-22.04-x64
                  os: linux
                steps:
                  - template: build/azure-pipelines/linux/cli-build-linux.yml@self
                    parameters:
                      VSCODE_CHECK_ONLY: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_LINUX: ${{ parameters.VSCODE_BUILD_LINUX }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true)) }}:
              - job: CLILinuxGnuARM
                pool:
                  name: 1es-ubuntu-22.04-x64
                  os: linux
                steps:
                  - template: build/azure-pipelines/linux/cli-build-linux.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_LINUX_ARMHF: ${{ parameters.VSCODE_BUILD_LINUX_ARMHF }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_LINUX_ARM64, true)) }}:
              - job: CLILinuxGnuAarch64
                pool:
                  name: 1es-ubuntu-22.04-x64
                  os: linux
                steps:
                  - template: build/azure-pipelines/linux/cli-build-linux.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_LINUX_ARM64: ${{ parameters.VSCODE_BUILD_LINUX_ARM64 }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_ALPINE, true)) }}:
              - job: CLIAlpineX64
                pool:
                  name: 1es-ubuntu-22.04-x64
                  os: linux
                steps:
                  - template: build/azure-pipelines/alpine/cli-build-alpine.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_ALPINE: ${{ parameters.VSCODE_BUILD_ALPINE }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true)) }}:
              - job: CLIAlpineARM64
                pool:
                  name: 1es-ubuntu-22.04-x64
                  os: linux
                steps:
                  - template: build/azure-pipelines/alpine/cli-build-alpine.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_ALPINE_ARM64: ${{ parameters.VSCODE_BUILD_ALPINE_ARM64 }}

            - ${{ if eq(parameters.VSCODE_BUILD_MACOS, true) }}:
              - job: CLIMacOSX64
                pool:
                  name: Azure Pipelines
                  image: macOS-13
                  os: macOS
                variables:
                  # todo@connor4312 to diagnose build flakes
                  - name: MSRUSTUP_LOG
                    value: debug
                steps:
                  - template: build/azure-pipelines/darwin/cli-build-darwin.yml@self
                    parameters:
                      VSCODE_CHECK_ONLY: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_MACOS: ${{ parameters.VSCODE_BUILD_MACOS }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_MACOS_ARM64, true)) }}:
              - job: CLIMacOSARM64
                pool:
                  name: Azure Pipelines
                  image: macOS-13
                  os: macOS
                variables:
                  # todo@connor4312 to diagnose build flakes
                  - name: MSRUSTUP_LOG
                    value: debug
                steps:
                  - template: build/azure-pipelines/darwin/cli-build-darwin.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_MACOS_ARM64: ${{ parameters.VSCODE_BUILD_MACOS_ARM64 }}

            - ${{ if eq(parameters.VSCODE_BUILD_WIN32, true) }}:
              - job: CLIWindowsX64
                pool:
                  name: 1es-windows-2019-x64
                  os: windows
                steps:
                  - template: build/azure-pipelines/win32/cli-build-win32.yml@self
                    parameters:
                      VSCODE_CHECK_ONLY: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_WIN32: ${{ parameters.VSCODE_BUILD_WIN32 }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
              - job: CLIWindowsARM64
                pool:
                  name: 1es-windows-2019-x64
                  os: windows
                steps:
                  - template: build/azure-pipelines/win32/cli-build-win32.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_WIN32_ARM64: ${{ parameters.VSCODE_BUILD_WIN32_ARM64 }}

      - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_COMPILE_ONLY, false)) }}:
        - stage: APIScan
          dependsOn: []
          pool:
            name: 1es-windows-2019-x64
            os: windows
          jobs:
            - job: WindowsAPIScan
              steps:
                - template: build/azure-pipelines/win32/sdl-scan-win32.yml@self
                  parameters:
                    VSCODE_ARCH: x64
                    VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}

      - ${{ if and(eq(parameters.VSCODE_COMPILE_ONLY, false), eq(variables['VSCODE_BUILD_STAGE_WINDOWS'], true)) }}:
        - stage: Windows
          dependsOn:
            - Compile
            - ${{ if or(eq(parameters.VSCODE_BUILD_LINUX, true),eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true),eq(parameters.VSCODE_BUILD_LINUX_ARM64, true),eq(parameters.VSCODE_BUILD_ALPINE, true),eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true),eq(parameters.VSCODE_BUILD_MACOS, true),eq(parameters.VSCODE_BUILD_MACOS_ARM64, true),eq(parameters.VSCODE_BUILD_WIN32, true),eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
              - CompileCLI
          pool:
            name: 1es-windows-2019-x64
            os: windows
          jobs:
            - ${{ if eq(variables['VSCODE_CIBUILD'], true) }}:
              - job: WindowsElectronTests
                displayName: Electron Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: x64
                steps:
                  - template: build/azure-pipelines/win32/product-build-win32.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_ARCH: x64
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: electron
                      VSCODE_RUN_ELECTRON_TESTS: true
              - job: WindowsBrowserTests
                displayName: Browser Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: x64
                steps:
                  - template: build/azure-pipelines/win32/product-build-win32.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_ARCH: x64
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: browser
                      VSCODE_RUN_BROWSER_TESTS: true
              - job: WindowsRemoteTests
                displayName: Remote Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: x64
                steps:
                  - template: build/azure-pipelines/win32/product-build-win32.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_ARCH: x64
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: remote
                      VSCODE_RUN_REMOTE_TESTS: true

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_WIN32, true)) }}:
              - job: Windows
                timeoutInMinutes: 120
                variables:
                  VSCODE_ARCH: x64
                templateContext:
                  sdl:
                    suppression:
                      suppressionFile: $(Build.SourcesDirectory)\.config\guardian\.gdnsuppress
                steps:
                  - template: build/azure-pipelines/win32/product-build-win32.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_ARCH: x64
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_RUN_ELECTRON_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}
                      VSCODE_RUN_BROWSER_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}
                      VSCODE_RUN_REMOTE_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}

              - job: WindowsCLISign
                timeoutInMinutes: 90
                steps:
                  - template: build/azure-pipelines/win32/product-build-win32-cli-sign.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_WIN32: ${{ parameters.VSCODE_BUILD_WIN32 }}
                      VSCODE_BUILD_WIN32_ARM64: ${{ parameters.VSCODE_BUILD_WIN32_ARM64 }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
              - job: WindowsARM64
                timeoutInMinutes: 90
                variables:
                  VSCODE_ARCH: arm64
                templateContext:
                  sdl:
                    suppression:
                      suppressionFile: $(Build.SourcesDirectory)\.config\guardian\.gdnsuppress
                steps:
                  - template: build/azure-pipelines/win32/product-build-win32.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_ARCH: arm64
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}

      - ${{ if and(eq(parameters.VSCODE_COMPILE_ONLY, false), eq(variables['VSCODE_BUILD_STAGE_LINUX'], true)) }}:
        - stage: Linux
          dependsOn:
            - Compile
            - ${{ if or(eq(parameters.VSCODE_BUILD_LINUX, true),eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true),eq(parameters.VSCODE_BUILD_LINUX_ARM64, true),eq(parameters.VSCODE_BUILD_ALPINE, true),eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true),eq(parameters.VSCODE_BUILD_MACOS, true),eq(parameters.VSCODE_BUILD_MACOS_ARM64, true),eq(parameters.VSCODE_BUILD_WIN32, true),eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
              - CompileCLI
          pool:
            name: 1es-ubuntu-22.04-x64
            os: linux
          jobs:
            - ${{ if eq(variables['VSCODE_CIBUILD'], true) }}:
              - job: Linuxx64ElectronTest
                displayName: Electron Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: x64
                  NPM_ARCH: x64
                  DISPLAY: ":10"
                steps:
                  - template: build/azure-pipelines/linux/product-build-linux.yml@self
                    parameters:
                      VSCODE_ARCH: x64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: electron
                      VSCODE_RUN_ELECTRON_TESTS: true
              - job: Linuxx64BrowserTest
                displayName: Browser Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: x64
                  NPM_ARCH: x64
                  DISPLAY: ":10"
                steps:
                  - template: build/azure-pipelines/linux/product-build-linux.yml@self
                    parameters:
                      VSCODE_ARCH: x64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: browser
                      VSCODE_RUN_BROWSER_TESTS: true
              - job: Linuxx64RemoteTest
                displayName: Remote Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: x64
                  NPM_ARCH: x64
                  DISPLAY: ":10"
                steps:
                  - template: build/azure-pipelines/linux/product-build-linux.yml@self
                    parameters:
                      VSCODE_ARCH: x64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: remote
                      VSCODE_RUN_REMOTE_TESTS: true

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_LINUX, true)) }}:
              - job: Linuxx64
                timeoutInMinutes: 90
                variables:
                  VSCODE_ARCH: x64
                  NPM_ARCH: x64
                  DISPLAY: ":10"
                steps:
                  - template: build/azure-pipelines/linux/product-build-linux.yml@self
                    parameters:
                      VSCODE_ARCH: x64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_RUN_ELECTRON_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}
                      VSCODE_RUN_BROWSER_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}
                      VSCODE_RUN_REMOTE_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true)) }}:
              - job: LinuxArmhf
                variables:
                  VSCODE_ARCH: armhf
                  NPM_ARCH: arm
                steps:
                  - template: build/azure-pipelines/linux/product-build-linux.yml@self
                    parameters:
                      VSCODE_ARCH: armhf
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_LINUX_ARM64, true)) }}:
              - job: LinuxArm64
                variables:
                  VSCODE_ARCH: arm64
                  NPM_ARCH: arm64
                steps:
                  - template: build/azure-pipelines/linux/product-build-linux.yml@self
                    parameters:
                      VSCODE_ARCH: arm64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}

      - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_COMPILE_ONLY, false), eq(variables['VSCODE_BUILD_STAGE_ALPINE'], true)) }}:
        - stage: Alpine
          dependsOn:
            - Compile
            - ${{ if or(eq(parameters.VSCODE_BUILD_LINUX, true),eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true),eq(parameters.VSCODE_BUILD_LINUX_ARM64, true),eq(parameters.VSCODE_BUILD_ALPINE, true),eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true),eq(parameters.VSCODE_BUILD_MACOS, true),eq(parameters.VSCODE_BUILD_MACOS_ARM64, true),eq(parameters.VSCODE_BUILD_WIN32, true),eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
              - CompileCLI
          pool:
            name: 1es-ubuntu-22.04-x64
            os: linux
          jobs:
            - ${{ if eq(parameters.VSCODE_BUILD_ALPINE, true) }}:
              - job: LinuxAlpine
                variables:
                  VSCODE_ARCH: x64
                  NPM_ARCH: x64
                steps:
                  - template: build/azure-pipelines/alpine/product-build-alpine.yml@self

            - ${{ if eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true) }}:
              - job: LinuxAlpineArm64
                timeoutInMinutes: 120
                variables:
                  VSCODE_ARCH: arm64
                  NPM_ARCH: arm64
                steps:
                  - template: build/azure-pipelines/alpine/product-build-alpine.yml@self

      - ${{ if and(eq(parameters.VSCODE_COMPILE_ONLY, false), eq(variables['VSCODE_BUILD_STAGE_MACOS'], true)) }}:
        - stage: macOS
          dependsOn:
            - Compile
            - ${{ if or(eq(parameters.VSCODE_BUILD_LINUX, true),eq(parameters.VSCODE_BUILD_LINUX_ARMHF, true),eq(parameters.VSCODE_BUILD_LINUX_ARM64, true),eq(parameters.VSCODE_BUILD_ALPINE, true),eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true),eq(parameters.VSCODE_BUILD_MACOS, true),eq(parameters.VSCODE_BUILD_MACOS_ARM64, true),eq(parameters.VSCODE_BUILD_WIN32, true),eq(parameters.VSCODE_BUILD_WIN32_ARM64, true)) }}:
              - CompileCLI
          pool:
            name: AcesShared
            os: macOS
          variables:
            BUILDSECMON_OPT_IN: true
          jobs:
            - ${{ if eq(variables['VSCODE_CIBUILD'], true) }}:
              - job: macOSElectronTest
                displayName: Electron Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: arm64
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin.yml@self
                    parameters:
                      VSCODE_ARCH: arm64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: electron
                      VSCODE_RUN_ELECTRON_TESTS: true
              - job: macOSBrowserTest
                displayName: Browser Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: arm64
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin.yml@self
                    parameters:
                      VSCODE_ARCH: arm64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: browser
                      VSCODE_RUN_BROWSER_TESTS: true
              - job: macOSRemoteTest
                displayName: Remote Tests
                timeoutInMinutes: 30
                variables:
                  VSCODE_ARCH: arm64
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin.yml@self
                    parameters:
                      VSCODE_ARCH: arm64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_TEST_ARTIFACT_NAME: remote
                      VSCODE_RUN_REMOTE_TESTS: true

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_MACOS, true)) }}:
              - job: macOS
                timeoutInMinutes: 90
                variables:
                  VSCODE_ARCH: x64
                  BUILDS_API_URL: $(System.CollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin.yml@self
                    parameters:
                      VSCODE_ARCH: x64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}

              - job: macOSCLI
                timeoutInMinutes: 90
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin-cli-sign.yml@self
                    parameters:
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_BUILD_MACOS: ${{ parameters.VSCODE_BUILD_MACOS }}
                      VSCODE_BUILD_MACOS_ARM64: ${{ parameters.VSCODE_BUILD_MACOS_ARM64 }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_BUILD_MACOS_ARM64, true)) }}:
              - job: macOSARM64
                timeoutInMinutes: 90
                variables:
                  VSCODE_ARCH: arm64
                  BUILDS_API_URL: $(System.CollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin.yml@self
                    parameters:
                      VSCODE_ARCH: arm64
                      VSCODE_QUALITY: ${{ variables.VSCODE_QUALITY }}
                      VSCODE_CIBUILD: ${{ variables.VSCODE_CIBUILD }}
                      VSCODE_RUN_ELECTRON_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}
                      VSCODE_RUN_BROWSER_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}
                      VSCODE_RUN_REMOTE_TESTS: ${{ eq(parameters.VSCODE_STEP_ON_IT, false) }}

            - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(variables['VSCODE_BUILD_MACOS_UNIVERSAL'], true)) }}:
              - job: macOSUniversal
                timeoutInMinutes: 90
                variables:
                  VSCODE_ARCH: universal
                  BUILDS_API_URL: $(System.CollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/
                steps:
                  - template: build/azure-pipelines/darwin/product-build-darwin-universal.yml@self

      - ${{ if and(eq(variables['VSCODE_CIBUILD'], false), eq(parameters.VSCODE_COMPILE_ONLY, false), eq(variables['VSCODE_BUILD_STAGE_WEB'], true)) }}:
        - stage: Web
          dependsOn:
            - Compile
          pool:
            name: 1es-ubuntu-22.04-x64
            os: linux
          jobs:
            - ${{ if eq(parameters.VSCODE_BUILD_WEB, true) }}:
              - job: Web
                variables:
                  VSCODE_ARCH: x64
                steps:
                  - template: build/azure-pipelines/web/product-build-web.yml@self

      - ${{ if eq(variables['VSCODE_PUBLISH'], 'true') }}:
        - stage: Publish
          dependsOn: []
          pool:
            name: 1es-windows-2019-x64
            os: windows
          variables:
            - name: BUILDS_API_URL
              value: $(System.CollectionUri)$(System.TeamProject)/_apis/build/builds/$(Build.BuildId)/
          jobs:
            - job: PublishBuild
              timeoutInMinutes: 180
              displayName: Publish Build
              steps:
                - template: build/azure-pipelines/product-publish.yml@self

        - ${{ if and(parameters.VSCODE_RELEASE, eq(variables['VSCODE_PRIVATE_BUILD'], false)) }}:
          - stage: ApproveRelease
            dependsOn: [] # run in parallel to compile stage
            pool:
              name: 1es-ubuntu-22.04-x64
              os: linux
            jobs:
              - job: ApproveRelease
                displayName: "Approve Release"
                variables:
                  - group: VSCodePeerApproval
                  - name: skipComponentGovernanceDetection
                    value: true

        - ${{ if or(and(parameters.VSCODE_RELEASE, eq(variables['VSCODE_PRIVATE_BUILD'], false)), and(in(parameters.VSCODE_QUALITY, 'insider', 'exploration'), eq(variables['VSCODE_SCHEDULEDBUILD'], true))) }}:
          - stage: Release
            dependsOn:
              - Publish
              - ${{ if and(parameters.VSCODE_RELEASE, eq(variables['VSCODE_PRIVATE_BUILD'], false)) }}:
                - ApproveRelease
            pool:
              name: 1es-ubuntu-22.04-x64
              os: linux
            jobs:
              - job: ReleaseBuild
                displayName: Release Build
                steps:
                  - template: build/azure-pipelines/product-release.yml@self
                    parameters:
                      VSCODE_RELEASE: ${{ parameters.VSCODE_RELEASE }}
