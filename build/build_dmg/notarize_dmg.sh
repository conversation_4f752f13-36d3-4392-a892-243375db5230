#!/bin/sh

#####################################################
# 脚本名称: notarize_dmg.sh
# 功能描述: 专门用于macOS DMG文件的公证和钉入票据
# 执行流程:
#   1. 提交DMG文件进行公证
#   2. 钉入票据(staple ticket)
# 使用方法:
#   ./notarize_dmg.sh [DMG文件路径]
# 注意事项:
#   - 需要有效的开发者ID证书
#   - 需要配置正确的Apple账号信息
#####################################################

#############################变量############################
# 当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 项目根目录
WORKSPACE="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Apple开发者账号信息
APPLE_ACCOUNT=<EMAIL>
APPLE_PASSWORD=lydo-ntxo-mmft-whtm
APPLE_TEAM_ID=FN2V63AD2J

# 处理命令行参数
if [ -z "$1" ]; then
    echo "错误：请提供DMG文件路径"
    echo "使用方法: $0 [DMG文件路径]"
    exit 1
fi

# DMG文件路径
DMG_PATH="$1"

# 检查DMG文件是否存在
if [ ! -f "$DMG_PATH" ]; then
    echo "错误：找不到DMG文件: $DMG_PATH"
    exit 1
fi

#############################函数############################
# 公证dmg
notaryDmgFile() {
    echo "正在公证DMG文件: $DMG_PATH..."
    result_response=$(xcrun notarytool submit --apple-id $APPLE_ACCOUNT --password $APPLE_PASSWORD --team-id $APPLE_TEAM_ID --wait "$DMG_PATH" 2>&1)
    echo "公证dmg-result_response:$result_response"
    if [[ $result_response != *"status: Accepted"* ]]; then
        echo "公证失败"
        id=$(perl -ne 'print "$1\n" if /id:\s*(\S+)/' <<< "$result_response")
        notaryDmgErrLog $id
        return 1
    fi
    return 0
}

# 查看公证日志
notaryDmgErrLog() {
    local log_id="$1"
    if [ -z "$log_id" ]; then
        echo "log_id不能为空"
        return 1
    fi
    echo "获取公证详细日志..."
    xcrun notarytool log --apple-id $APPLE_ACCOUNT --password $APPLE_PASSWORD --team-id $APPLE_TEAM_ID $log_id
}

# 钉入票据
stapleDmgFile() {
    echo "正在钉入票据到DMG: $DMG_PATH..."

    # 设置最大重试次数
    local max_attempts=3
    local attempt=1
    local success=false

    while [ $attempt -le $max_attempts ] && [ "$success" = false ]; do
        echo "尝试钉入票据 (第 $attempt 次，共 $max_attempts 次)..."

        # 添加verbose参数获取更详细的错误信息
        xcrun stapler staple --verbose "$DMG_PATH"

        if [ $? -eq 0 ]; then
            echo "成功钉入票据到DMG文件"
            # 验证票据是否成功钉入
            xcrun stapler validate "$DMG_PATH"
            success=true
            return 0
        else
            if [ $attempt -lt $max_attempts ]; then
                echo "钉入票据失败，等待30秒后重试..."
                sleep 30
            else
                echo "警告：钉入票据失败，但将继续执行。会导致app需要联网验证才能使用"
                # 不再退出，继续执行
                return 1
            fi
            attempt=$((attempt+1))
        fi
    done
}

#############################主逻辑############################

# 开始执行脚本
echo "开始执行DMG公证和票据钉入脚本"
echo "处理DMG文件: $DMG_PATH"

# 公证
if notaryDmgFile; then
    echo "DMG公证成功"
else
    echo "DMG公证失败，但将继续尝试钉入票据"
fi

# 钉入票据
if stapleDmgFile; then
    echo "票据钉入成功"
else
    echo "票据钉入可能未完全成功，请检查上述日志"
fi

echo "DMG公证和票据钉入任务完成"
