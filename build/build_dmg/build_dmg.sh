#!/bin/sh

#####################################################
# 脚本名称: build_dmg.sh
# 功能描述: 用于macOS应用的签名、打包、公证和钉入票据
# 执行流程:
#   1. 使用共用脚本签名应用程序及其所有组件
#   2. 打包DMG镜像
#   3. 提交公证
#   4. 钉入票据(staple ticket)
# 注意事项:
#   - 需要有效的开发者ID证书
#   - 需要配置正确的Apple账号信息
#   - 需要相应的entitlements文件
#####################################################

#############################变量############################
# 当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 项目根目录
WORKSPACE="$(cd "$SCRIPT_DIR/../.." && pwd)"
# 共用脚本目录
COMMON_SCRIPTS="$WORKSPACE/build/common"

BUILD_PATH="build"
APP_PATH="app"
RESOURCES_PATH="resources"
APP_TARGET_VERSION=0.0.1
NAME="CodeBuddy"
#.app名称
APP_NAME="$NAME.app"

# 处理命令行参数
if [ -n "$1" ]; then
    # 如果提供了应用路径参数
    APP_MAIN_PATH="$1"
else
    # 使用默认路径
    APP_MAIN_PATH="$APP_PATH/$APP_NAME"
fi

DMG_NAME=codebuddy-$APP_TARGET_VERSION.dmg
DMG_BUILD_PATH="$BUILD_PATH/dmg_build"
#临时dmg
TEMP_DMG_PATH="$BUILD_PATH/tmp.dmg"
#背景图片名
BG_DMG_NAME="bg_tencent_dmg.png"
#背景图路径
BG_DMG_PATH="$RESOURCES_PATH/$BG_DMG_NAME"
#最终dmg路径
TARGET_DMG_PATH="$BUILD_PATH/$DMG_NAME"
#图片保存的位置，在DMG_BUILD_PATH内，加.代表隐藏
BG_DMG_BUILD_PATH="$DMG_BUILD_PATH/.background"
#复制过来的app原位置 setEnv设置
APP_SOURCE_PATH="$APP_PATH/$APP_NAME"
#app复制后的位置
APP_TARGET_PATH="$DMG_BUILD_PATH/$APP_NAME"
#Applications替身位置
APPLICATION_LN_PATH="$DMG_BUILD_PATH/Applications"


#############################常量############################
#项目路径
WORKSPACE=$(cd $(dirname "$0"); pwd)
APPLE_KEY_P12="Developer ID Application: Tencent Technology (Shanghai) Co., Ltd (FN2V63AD2J)"
APPLE_ACCOUNT=<EMAIL>
APPLE_PASSWORD=lydo-ntxo-mmft-whtm
APPLE_TEAM_ID=FN2V63AD2J

#############################函数############################
# 打包脚本, 本地和流水线都可以
packageDmg() {
    echo "开始打包dmg..."

    # 1. 处理build目录
    if [ -d "build" ]; then
        echo "清理已存在的build目录..."
        rm -rf build
    fi
    mkdir -p build
    chmod 1777 build

    # 2. 检查源文件是否存在
    if [ ! -d "$APP_MAIN_PATH" ]; then
        echo "错误：找不到要打包的.app文件: $APP_MAIN_PATH"
        exit 1
    fi

    # 3. 检查dmgbuild是否安装
    if ! command -v dmgbuild &> /dev/null; then
        echo "dmgbuild 未安装，正在尝试自动安装..."
        if command -v brew &> /dev/null; then
            echo "使用 Homebrew 安装 dmgbuild..."
            brew install dmgbuild
        elif command -v pipx &> /dev/null; then
            echo "使用 pipx 安装 dmgbuild..."
            pipx install dmgbuild
        else
            echo "尝试用 pip --user 安装 dmgbuild..."
            pip3 install --user dmgbuild
            export PATH="$HOME/.local/bin:$PATH"
        fi
        # 再次检查是否安装成功
        if ! command -v dmgbuild &> /dev/null; then
            echo "dmgbuild 安装失败，请手动安装："
            echo "推荐命令：brew install dmgbuild 或 pipx install dmgbuild"
            exit 1
        else
            echo "dmgbuild 安装成功"
        fi
    fi

    # 4. 执行打包
    echo "正在打包 $APP_MAIN_PATH 到 build/${DMG_NAME}..."
    dmgbuild -s dmg_settings.py \
         -D files=["$APP_MAIN_PATH"] \
         -D bg_image="${BG_DMG_PATH}" \
         "${NAME}" "build/${DMG_NAME}"

    echo "打包dmg完成"
}

# 公证dmg
notaryDmgFile() {
    echo "正在公证DMG文件..."
    result_response=$(xcrun notarytool submit --apple-id $APPLE_ACCOUNT --password $APPLE_PASSWORD --team-id $APPLE_TEAM_ID --wait $TARGET_DMG_PATH 2>&1)
    echo "公证dmg-result_response:$result_response"
    if [[ $result_response != *"status: Accepted"* ]]; then
        echo "公证失败"
        id=$(perl -ne 'print "$1\n" if /id:\s*(\S+)/' <<< "$result_response")
        notaryDmgErrLog $id
        exit 0;
    fi
}

# 查看公证日志
notaryDmgErrLog() {
    local log_id="$1"
    if [ -z "$log_id" ]; then
        echo "log_id不能为空"
        exit 0;
    fi
    xcrun notarytool log --apple-id $APPLE_ACCOUNT --password $APPLE_PASSWORD --team-id $APPLE_TEAM_ID $log_id
}

# 钉入票据
stapleDmgFile() {
    echo "正在钉入票据到DMG..."

    # 设置最大重试次数
    local max_attempts=3
    local attempt=1
    local success=false

    while [ $attempt -le $max_attempts ] && [ "$success" = false ]; do
        echo "尝试钉入票据 (第 $attempt 次，共 $max_attempts 次)..."

        # 添加verbose参数获取更详细的错误信息
        xcrun stapler staple --verbose "$TARGET_DMG_PATH"

        if [ $? -eq 0 ]; then
            echo "成功钉入票据到DMG文件"
            # 验证票据是否成功钉入
            xcrun stapler validate "$TARGET_DMG_PATH"
            success=true
        else
            if [ $attempt -lt $max_attempts ]; then
                echo "钉入票据失败，等待30秒后重试..."
                sleep 30
            else
                echo "警告：钉入票据失败，但将继续执行。会导致app需要联网验证才能使用"
                # 不再退出，继续执行
            fi
            attempt=$((attempt+1))
        fi
    done
}

#############################主逻辑############################

# 开始执行脚本
echo "开始执行DMG打包和公证脚本"
cd ${WORKSPACE}

# 加载共用签名脚本
if [ -f "$COMMON_SCRIPTS/sign_app.sh" ]; then
    source "$COMMON_SCRIPTS/sign_app.sh"

    # 签名应用程序
    echo "开始签名应用程序..."
    if signApp "$APP_MAIN_PATH"; then
        echo "应用程序签名成功"
    else
        echo "错误：应用程序签名失败"
        exit 1
    fi
else
    echo "错误：找不到共用签名脚本: $COMMON_SCRIPTS/sign_app.sh"
    exit 1
fi

# 打包dmg
packageDmg

# 公证
notaryDmgFile

# 钉入票据
stapleDmgFile

echo "DMG打包和公证任务完成，执行成功~"
