#!/bin/bash
#############################
# ZIP打包和公证脚本
# 该脚本用于打包和公证macOS应用程序
# 使用方法: ./build_zip.sh [应用程序路径] [输出ZIP文件名]
# 例如: ./build_zip.sh ../build_dmg/app/CodeBuddy.app codebuddy.zip
#############################

#############################变量############################
# 当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 项目根目录
WORKSPACE="$(cd "$SCRIPT_DIR/../.." && pwd)"
# 构建目录
BUILD_PATH="$SCRIPT_DIR/build"
# 资源目录
RESOURCES_PATH="$WORKSPACE/build/resources"
# 共用脚本目录
COMMON_SCRIPTS="$WORKSPACE/build/common"

# 处理参数
if [ -n "$1" ]; then
    # 将相对路径转换为绝对路径
    if [[ "$1" = /* ]]; then
        # 如果是绝对路径，直接使用
        APP_MAIN_PATH="$1"
    else
        # 如果是相对路径，转换为绝对路径
        APP_MAIN_PATH="$(cd "$(dirname "$1")" && pwd)/$(basename "$1")"
    fi
    # 从路径中提取应用名称
    APP_NAME=$(basename "$APP_MAIN_PATH")
    APP_PATH=$(dirname "$APP_MAIN_PATH")
else
    # 默认路径
    APP_PATH="app"
    APP_NAME="CodeBuddy.app"
    APP_MAIN_PATH="$APP_PATH/$APP_NAME"
fi

# 如果没有提供输出文件名，使用默认名称
OUTPUT_ZIP_NAME="${2:-codebuddy.zip}"
echo "输出ZIP文件名: $OUTPUT_ZIP_NAME"
# 最终ZIP文件路径
ZIP_FILE_PATH="$OUTPUT_ZIP_NAME"

#############################常量############################
# 开发者账号信息
APPLE_KEY_P12="Developer ID Application: Tencent Technology (Shanghai) Co., Ltd (FN2V63AD2J)"
APPLE_ACCOUNT=<EMAIL>
APPLE_PASSWORD=lydo-ntxo-mmft-whtm
APPLE_TEAM_ID=FN2V63AD2J

#############################函数############################

# 公证ZIP文件
notarizeZipFile() {
    echo "正在公证ZIP文件: $ZIP_FILE_PATH..."

    # 检查ZIP文件是否存在
    if [ ! -f "$ZIP_FILE_PATH" ]; then
        echo "错误：ZIP文件不存在: $ZIP_FILE_PATH"
        exit 1
    fi

    # 提交公证请求并等待结果
    result_response=$(xcrun notarytool submit --apple-id $APPLE_ACCOUNT --password $APPLE_PASSWORD --team-id $APPLE_TEAM_ID --wait "$ZIP_FILE_PATH" 2>&1)
    echo "公证ZIP结果: $result_response"

    # 检查公证结果
    if [[ $result_response != *"status: Accepted"* ]]; then
        echo "公证失败"
        # 提取日志ID以查看详细错误信息
        log_id=$(echo "$result_response" | grep -o 'id: [a-zA-Z0-9-]*' | head -1 | cut -d' ' -f2)
        if [ -n "$log_id" ]; then
            echo "获取公证详细日志，ID: $log_id"
            xcrun notarytool log --apple-id $APPLE_ACCOUNT --password $APPLE_PASSWORD --team-id $APPLE_TEAM_ID $log_id
        fi
        return 1
    else
        echo "公证成功"
        return 0
    fi
}

#############################主逻辑############################

# 开始执行脚本
echo "开始执行ZIP打包和公证脚本"
echo "应用程序路径: $APP_MAIN_PATH"
echo "ZIP输出路径: $ZIP_FILE_PATH"

# 保存当前目录
ORIGINAL_DIR="$(pwd)"

# 切换到工作空间目录
cd ${WORKSPACE}

# 创建构建目录
if [ ! -d "$BUILD_PATH" ]; then
    mkdir -p "$BUILD_PATH"
fi

# 检查应用是否存在
if [ ! -d "$APP_MAIN_PATH" ]; then
    echo "错误：应用程序不存在: $APP_MAIN_PATH"
    exit 1
fi

# 加载共用签名脚本
if [ -f "$COMMON_SCRIPTS/sign_app.sh" ]; then
    source "$COMMON_SCRIPTS/sign_app.sh"
else
    echo "错误：找不到共用签名脚本: $COMMON_SCRIPTS/sign_app.sh"
    exit 1
fi

# 签名应用程序
echo "开始签名应用程序..."
if signApp "$APP_MAIN_PATH"; then
    echo "应用程序签名成功"
else
    echo "错误：应用程序签名失败"
    exit 1
fi

# 创建ZIP包
echo "正在创建ZIP包..."
# 使用ditto命令创建ZIP包，更好地处理macOS资源分支和元数据
if ditto -c -k --sequesterRsrc --keepParent "$APP_MAIN_PATH" "$ZIP_FILE_PATH"; then
    echo "使用ditto成功创建ZIP包"
else
    echo "使用ditto创建ZIP包失败，尝试使用zip命令..."
    # 保存当前目录
    CURRENT_DIR="$(pwd)"
    # 切换到应用所在目录
    cd "$(dirname "$APP_MAIN_PATH")"
    zip -r "$WORKSPACE/$ZIP_FILE_PATH" "$(basename "$APP_MAIN_PATH")" -x "*.DS_Store" -x "__MACOSX"
    # 返回之前的目录
    cd "$CURRENT_DIR"
fi

# 检查ZIP是否创建成功
if [ ! -f "$ZIP_FILE_PATH" ]; then
    echo "错误：ZIP包创建失败"
    exit 1
fi
echo "ZIP包创建成功: $ZIP_FILE_PATH"

# 公证ZIP文件
if notarizeZipFile; then
    echo "ZIP公证成功"
else
    echo "警告：ZIP公证失败，但将继续执行"
fi

# 返回原始目录
cd "$ORIGINAL_DIR"

echo "ZIP打包和公证任务完成，执行成功"
exit 0
